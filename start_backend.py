#!/usr/bin/env python3
"""
HealNav Backend Startup Script
"""
import os
import sys
import subprocess
from pathlib import Path

def check_requirements():
    """Check if all required packages are installed"""
    try:
        import fastapi
        import uvicorn
        import sqlalchemy
        import groq
        import langgraph
        import geopy
        print("✅ All required packages are installed")
        return True
    except ImportError as e:
        print(f"❌ Missing package: {e}")
        print("Please install requirements: pip install -r requirements.txt")
        return False

def check_env_file():
    """Check if .env file exists"""
    if not os.path.exists('.env'):
        print("⚠️  .env file not found. Creating from template...")
        if os.path.exists('.env.example'):
            import shutil
            shutil.copy('.env.example', '.env')
            print("✅ Created .env file from template")
            print("📝 Please edit .env file and add your GROQ_API_KEY")
        else:
            print("❌ .env.example not found")
        return False
    return True

def start_backend():
    """Start the FastAPI backend server"""
    print("🚀 Starting HealNav Backend Server...")
    
    # Change to backend directory
    backend_dir = Path(__file__).parent / "backend"
    os.chdir(backend_dir)
    
    # Start uvicorn server
    try:
        subprocess.run([
            sys.executable, "-m", "uvicorn", 
            "main:app", 
            "--host", "0.0.0.0", 
            "--port", "8000", 
            "--reload"
        ], check=True)
    except KeyboardInterrupt:
        print("\n🛑 Backend server stopped")
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to start backend: {e}")

if __name__ == "__main__":
    print("🏥 HealNav Backend Startup")
    print("=" * 40)
    
    if not check_requirements():
        sys.exit(1)
    
    if not check_env_file():
        print("Please configure .env file and restart")
        sys.exit(1)
    
    start_backend()
