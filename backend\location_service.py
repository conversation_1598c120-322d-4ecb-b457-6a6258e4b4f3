from geopy.distance import geodesic
from geopy.geocoders import Nominatim
from typing import Tuple, Optional, List, Dict
import requests
import json

class LocationService:
    def __init__(self):
        self.geolocator = Nominatim(user_agent="healNav")
    
    def calculate_distance(self, lat1: float, lon1: float, lat2: float, lon2: float) -> float:
        """Calculate distance between two points in kilometers"""
        point1 = (lat1, lon1)
        point2 = (lat2, lon2)
        return geodesic(point1, point2).kilometers
    
    def geocode_address(self, address: str) -> Optional[Tuple[float, float]]:
        """Convert address to latitude and longitude"""
        try:
            location = self.geolocator.geocode(address)
            if location:
                return (location.latitude, location.longitude)
            return None
        except Exception as e:
            print(f"Geocoding error: {e}")
            return None
    
    def reverse_geocode(self, latitude: float, longitude: float) -> Optional[str]:
        """Convert coordinates to address"""
        try:
            location = self.geolocator.reverse((latitude, longitude))
            if location:
                return location.address
            return None
        except Exception as e:
            print(f"Reverse geocoding error: {e}")
            return None
    
    def find_nearby_points(self, user_lat: float, user_lon: float, 
                          points: List[Dict], radius_km: float = 10.0) -> List[Dict]:
        """Find points within radius and add distance information"""
        nearby_points = []
        user_location = (user_lat, user_lon)
        
        for point in points:
            point_location = (point['latitude'], point['longitude'])
            distance = geodesic(user_location, point_location).kilometers
            
            if distance <= radius_km:
                point_with_distance = point.copy()
                point_with_distance['distance_km'] = round(distance, 2)
                nearby_points.append(point_with_distance)
        
        # Sort by distance
        nearby_points.sort(key=lambda x: x['distance_km'])
        return nearby_points
    
    def get_city_from_coordinates(self, latitude: float, longitude: float) -> str:
        """Extract city name from coordinates"""
        try:
            location = self.geolocator.reverse((latitude, longitude))
            if location and location.raw.get('address'):
                address = location.raw['address']
                city = address.get('city') or address.get('town') or address.get('village')
                return city or "Unknown City"
            return "Unknown City"
        except Exception as e:
            print(f"City extraction error: {e}")
            return "Unknown City"
    
    def validate_coordinates(self, latitude: float, longitude: float) -> bool:
        """Validate if coordinates are within valid ranges"""
        return (-90 <= latitude <= 90) and (-180 <= longitude <= 180)
    
    def get_bounding_box(self, center_lat: float, center_lon: float, radius_km: float) -> Dict[str, float]:
        """Get bounding box coordinates for a given center point and radius"""
        # Approximate conversion: 1 degree ≈ 111 km
        lat_delta = radius_km / 111.0
        lon_delta = radius_km / (111.0 * abs(center_lat))
        
        return {
            'north': center_lat + lat_delta,
            'south': center_lat - lat_delta,
            'east': center_lon + lon_delta,
            'west': center_lon - lon_delta
        }

# Utility functions for common location operations
def calculate_travel_time(start_lat: float, start_lon: float, 
                         end_lat: float, end_lon: float, mode: str = "driving") -> Optional[Dict]:
    """
    Estimate travel time between two points
    Note: This is a simplified version. In production, you'd use Google Maps API or similar
    """
    distance_km = geodesic((start_lat, start_lon), (end_lat, end_lon)).kilometers
    
    # Rough estimates based on mode of transport
    speed_estimates = {
        "walking": 5,    # km/h
        "driving": 40,   # km/h (city average)
        "transit": 25    # km/h (public transport average)
    }
    
    speed = speed_estimates.get(mode, 40)
    time_hours = distance_km / speed
    time_minutes = int(time_hours * 60)
    
    return {
        "distance_km": round(distance_km, 2),
        "estimated_time_minutes": time_minutes,
        "mode": mode
    }

def format_distance(distance_km: float) -> str:
    """Format distance for display"""
    if distance_km < 1:
        return f"{int(distance_km * 1000)}m"
    else:
        return f"{distance_km:.1f}km"
