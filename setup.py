#!/usr/bin/env python3
"""
HealNav Setup and Installation Script
"""
import os
import sys
import subprocess
import shutil
from pathlib import Path

def install_requirements():
    """Install Python requirements"""
    print("📦 Installing Python requirements...")
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], check=True)
        print("✅ Requirements installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install requirements: {e}")
        return False

def setup_environment():
    """Setup environment file"""
    print("⚙️  Setting up environment...")
    
    if not os.path.exists('.env'):
        if os.path.exists('.env.example'):
            shutil.copy('.env.example', '.env')
            print("✅ Created .env file from template")
        else:
            # Create basic .env file
            with open('.env', 'w') as f:
                f.write("GROQ_API_KEY=your_groq_api_key_here\n")
                f.write("DATABASE_URL=sqlite:///./healNav.db\n")
                f.write("API_HOST=localhost\n")
                f.write("API_PORT=8000\n")
                f.write("FRONTEND_PORT=8501\n")
                f.write("DEFAULT_SEARCH_RADIUS_KM=50\n")
            print("✅ Created basic .env file")
    else:
        print("✅ .env file already exists")
    
    return True

def create_directories():
    """Create necessary directories"""
    print("📁 Creating directories...")
    
    directories = ['backend', 'frontend', 'logs']
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
    
    print("✅ Directories created")
    return True

def check_python_version():
    """Check Python version"""
    print("🐍 Checking Python version...")
    
    if sys.version_info < (3, 8):
        print("❌ Python 3.8+ is required")
        return False
    
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor} is supported")
    return True

def main():
    """Main setup function"""
    print("🏥 HealNav Setup")
    print("=" * 40)
    
    steps = [
        ("Checking Python version", check_python_version),
        ("Creating directories", create_directories),
        ("Installing requirements", install_requirements),
        ("Setting up environment", setup_environment),
    ]
    
    for step_name, step_func in steps:
        print(f"\n{step_name}...")
        if not step_func():
            print(f"❌ Setup failed at: {step_name}")
            sys.exit(1)
    
    print("\n" + "=" * 40)
    print("🎉 HealNav setup completed successfully!")
    print("\nNext steps:")
    print("1. Edit .env file and add your GROQ_API_KEY")
    print("2. Run: python start_app.py")
    print("3. Open http://localhost:8501 in your browser")
    print("\nFor testing: python test_api.py")

if __name__ == "__main__":
    main()
