import streamlit as st
import requests
import folium
from streamlit_folium import st_folium
import json
from typing import Dict, List
import time

# Page configuration
st.set_page_config(
    page_title="HealNav - Find Medical Care",
    page_icon="🏥",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Configuration
API_BASE_URL = "http://localhost:8000"

# Custom CSS for clean design
st.markdown("""
<style>
    .main-header {
        text-align: center;
        color: #2E86AB;
        font-size: 3rem;
        font-weight: bold;
        margin-bottom: 1rem;
    }
    .sub-header {
        text-align: center;
        color: #666;
        font-size: 1.2rem;
        margin-bottom: 2rem;
    }
    .doctor-card {
        background: #f8f9fa;
        padding: 1rem;
        border-radius: 10px;
        border-left: 4px solid #2E86AB;
        margin: 1rem 0;
    }
    .emergency-alert {
        background: #ff4444;
        color: white;
        padding: 1rem;
        border-radius: 10px;
        text-align: center;
        font-weight: bold;
        margin: 1rem 0;
    }
    .ai-recommendation {
        background: #e8f4fd;
        padding: 1rem;
        border-radius: 10px;
        border-left: 4px solid #2E86AB;
        margin: 1rem 0;
    }
</style>
""", unsafe_allow_html=True)

def check_api_health():
    """Check if backend API is running"""
    try:
        response = requests.get(f"{API_BASE_URL}/health", timeout=5)
        return response.status_code == 200
    except:
        return False

def search_medical_facilities(latitude: float, longitude: float, query: str = "", radius: float = 10.0):
    """Search for medical facilities using the backend API"""
    try:
        payload = {
            "latitude": latitude,
            "longitude": longitude,
            "query": query,
            "radius_km": radius
        }

        response = requests.post(f"{API_BASE_URL}/search", json=payload, timeout=30)

        if response.status_code == 200:
            return response.json()
        else:
            st.error(f"API Error: {response.status_code}")
            return None

    except requests.exceptions.RequestException as e:
        st.error(f"Connection error: {str(e)}")
        return None

def display_doctor_card(doctor: Dict):
    """Display a doctor information card"""
    with st.container():
        st.markdown(f"""
        <div class="doctor-card">
            <h4>👨‍⚕️ {doctor['name']}</h4>
            <p><strong>🏥 Hospital:</strong> {doctor['hospital']}</p>
            <p><strong>🩺 Specialization:</strong> {doctor['specialization']}</p>
            <p><strong>📍 Distance:</strong> {doctor['distance_km']} km</p>
            <p><strong>📍 Address:</strong> {doctor['address']}</p>
            {f"<p><strong>⭐ Rating:</strong> {doctor['rating']}/5</p>" if doctor.get('rating') else ""}
            {f"<p><strong>📞 Phone:</strong> {doctor['phone']}</p>" if doctor.get('phone') else ""}
        </div>
        """, unsafe_allow_html=True)

def main():
    # Header
    st.markdown('<h1 class="main-header">🏥 HealNav</h1>', unsafe_allow_html=True)
    st.markdown('<p class="sub-header">AI-Powered Medical Care Finder</p>', unsafe_allow_html=True)

    # Check API health
    api_healthy = check_api_health()
    if not api_healthy:
        st.error("⚠️ Backend API is not running. Please start the FastAPI server first.")
        st.info("Run: `cd backend && python main.py`")
        return

    # Initialize session state
    if 'search_results' not in st.session_state:
        st.session_state.search_results = None

    # Sidebar for user input
    with st.sidebar:
        st.header("📍 Your Location")

        # Location input methods
        location_method = st.radio(
            "How would you like to provide your location?",
            ["Manual Input", "Use Current Location", "Select State & City"]
        )

        if location_method == "Manual Input":
            latitude = st.number_input("Latitude", value=40.7128, format="%.6f")
            longitude = st.number_input("Longitude", value=-74.0060, format="%.6f")
        elif location_method == "Select State & City":
            # Indian states and cities
            indian_cities = {
                "Delhi": {
                    "New Delhi": (28.6139, 77.2090),
                    "Dwarka": (28.5921, 77.0460),
                    "Rohini": (28.7041, 77.1025)
                },
                "Maharashtra": {
                    "Mumbai": (19.0760, 72.8777),
                    "Pune": (18.5204, 73.8567),
                    "Nagpur": (21.1458, 79.0882)
                },
                "Karnataka": {
                    "Bangalore": (12.9716, 77.5946),
                    "Mysore": (12.2958, 76.6394),
                    "Mangalore": (12.9141, 74.8560)
                },
                "Tamil Nadu": {
                    "Chennai": (13.0827, 80.2707),
                    "Coimbatore": (11.0168, 76.9558),
                    "Madurai": (9.9252, 78.1198)
                },
                "Telangana": {
                    "Hyderabad": (17.3850, 78.4867),
                    "Warangal": (17.9689, 79.5941),
                    "Nizamabad": (18.6725, 78.0941)
                }
            }

            selected_state = st.selectbox("Select State:", list(indian_cities.keys()))
            selected_city = st.selectbox("Select City:", list(indian_cities[selected_state].keys()))
            latitude, longitude = indian_cities[selected_state][selected_city]
            st.info(f"📍 {selected_city}, {selected_state}: {latitude:.4f}, {longitude:.4f}")
        else:
            st.info("📱 Click 'Get Current Location' to use your device's GPS")
            if st.button("Get Current Location"):
                st.warning("Location access requires HTTPS. Using default NYC location for demo.")
                latitude, longitude = 40.7128, -74.0060
            else:
                latitude, longitude = 40.7128, -74.0060

        st.divider()

        # Medical query input
        st.header("🔍 What do you need?")
        medical_query = st.text_area(
            "Describe your medical needs:",
            placeholder="e.g., I need a cardiologist for chest pain, or I'm looking for a pediatrician near me",
            help="Be specific about your symptoms or the type of specialist you need"
        )

        # Quick query buttons
        st.write("**Quick Options:**")
        col1, col2 = st.columns(2)
        with col1:
            if st.button("🫀 Cardiology", use_container_width=True):
                medical_query = "I need a cardiologist"
            if st.button("👶 Pediatrics", use_container_width=True):
                medical_query = "I need a pediatrician"
        with col2:
            if st.button("🦴 Orthopedics", use_container_width=True):
                medical_query = "I need an orthopedic doctor"
            if st.button("🧠 Neurology", use_container_width=True):
                medical_query = "I need a neurologist"

        search_radius = st.slider("Search radius (km)", 1, 50, 10)

        search_button = st.button("🔍 Find Medical Care", type="primary", use_container_width=True)

    # Main content area
    col1, col2 = st.columns([1, 1])

    with col1:
        st.subheader("📍 Your Location")
        # Create a map showing user location
        m = folium.Map(location=[latitude, longitude], zoom_start=12)
        folium.Marker(
            [latitude, longitude],
            popup="Your Location",
            icon=folium.Icon(color="red", icon="user")
        ).add_to(m)

        # Add doctor markers if search results exist
        if st.session_state.search_results and st.session_state.search_results.get('doctors'):
            for doctor in st.session_state.search_results['doctors'][:10]:  # Show max 10 on map
                folium.Marker(
                    [doctor['latitude'], doctor['longitude']],
                    popup=f"{doctor['name']}<br>{doctor['specialization']}<br>{doctor['distance_km']}km",
                    icon=folium.Icon(color="blue", icon="plus")
                ).add_to(m)

        map_data = st_folium(m, width=400, height=400)

    with col2:
        st.subheader("🔍 Doctor Search")
        if medical_query:
            st.info(f"🔍 Looking for: {medical_query}")
        else:
            st.info("Please describe your medical needs in the sidebar")

        # Show API status
        st.success("✅ Backend API is running")

    # Search functionality
    if search_button:
        if not medical_query.strip():
            st.warning("Please describe your medical needs before searching.")
        else:
            st.divider()
            st.subheader("🏥 Search Results")

            with st.spinner("🔍 Searching for medical facilities..."):
                results = search_medical_facilities(latitude, longitude, medical_query, search_radius)
                st.session_state.search_results = results

            if results:
                # Display precaution
                if results.get('ai_recommendation'):
                    st.markdown(f"""
                    <div class="ai-recommendation">
                        <h4>⚠️ General Precaution</h4>
                        <p>{results['ai_recommendation']}</p>
                    </div>
                    """, unsafe_allow_html=True)

                # Display results summary
                total_found = results.get('total_found', 0)
                if total_found > 0:
                    st.success(f"Found {total_found} medical facilities within {search_radius}km")

                    # Display doctor cards
                    for doctor in results['doctors']:
                        display_doctor_card(doctor)
                else:
                    st.warning("No medical facilities found in your area. Try expanding your search radius.")
            else:
                st.error("Search failed. Please try again.")

    # Display existing results if available
    elif st.session_state.search_results:
        st.divider()
        st.subheader("🏥 Previous Search Results")
        results = st.session_state.search_results

        if results.get('ai_recommendation'):
            st.markdown(f"""
            <div class="ai-recommendation">
                <h4>⚠️ General Precaution</h4>
                <p>{results['ai_recommendation']}</p>
            </div>
            """, unsafe_allow_html=True)

        for doctor in results.get('doctors', []):
            display_doctor_card(doctor)

if __name__ == "__main__":
    main()
