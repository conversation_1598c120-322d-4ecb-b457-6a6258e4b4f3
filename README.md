# 🏥 HealNav - AI-Powered Medical Care Finder

Heal<PERSON><PERSON> is an intelligent medical facility finder that uses AI to help patients locate the nearest doctors and hospitals based on their current location and medical needs.

## 🌟 Features

- **🤖 AI-Powered Search**: Uses LangGraph and Groq for intelligent medical query processing
- **📍 Location-Based**: Find medical facilities based on your current location
- **🩺 Specialization Matching**: Automatically matches your symptoms to the right specialists
- **⚡ Real-Time Recommendations**: Get instant AI recommendations for your medical needs
- **🗺️ Interactive Maps**: Visual representation of nearby medical facilities
- **🚨 Emergency Detection**: Automatically detects urgent medical situations

## 🛠️ Technology Stack

- **Backend**: FastAPI with SQLAlchemy
- **Frontend**: Streamlit with interactive maps
- **AI Engine**: LangGraph for agentic AI system
- **LLM**: Groq (open-source model)
- **Location Services**: Geopy for distance calculations
- **Database**: SQLite (easily replaceable)

## 🚀 Quick Start

### Prerequisites

- Python 3.8+
- Groq API Key (free from [Groq](https://groq.com))

### Installation

1. **Clone and setup**:
   ```bash
   cd Medico
   pip install -r requirements.txt
   ```

2. **Configure environment**:
   ```bash
   cp .env.example .env
   # Edit .env and add your GROQ_API_KEY
   ```

3. **Start the application**:
   ```bash
   # Option 1: Start everything together
   python start_app.py
   
   # Option 2: Start separately
   python start_backend.py  # Terminal 1
   python start_frontend.py # Terminal 2
   ```

4. **Access the application**:
   - Frontend: http://localhost:8501
   - Backend API: http://localhost:8000
   - API Docs: http://localhost:8000/docs

## 🧪 Testing

Run the test suite to verify everything is working:

```bash
python test_api.py
```

## 📖 Usage

1. **Set Your Location**: 
   - Enter coordinates manually
   - Select from popular cities
   - Use GPS (requires HTTPS)

2. **Describe Your Needs**:
   - Type your medical query naturally
   - Use quick buttons for common specialties
   - Be specific about symptoms

3. **Get AI Recommendations**:
   - Receive intelligent suggestions
   - See nearby doctors sorted by distance
   - Get emergency alerts when needed

## 🏗️ Project Structure

```
HealNav/
├── backend/
│   ├── main.py              # FastAPI application
│   ├── models.py            # Database models
│   ├── database.py          # Database operations
│   ├── agent_system.py      # LangGraph AI agent
│   ├── groq_service.py      # Groq integration
│   └── location_service.py  # Location utilities
├── frontend/
│   └── app.py              # Streamlit application
├── requirements.txt        # Python dependencies
├── .env.example           # Environment template
├── start_app.py           # Complete app launcher
├── start_backend.py       # Backend launcher
├── start_frontend.py      # Frontend launcher
├── test_api.py           # API testing suite
└── README.md             # This file
```

## 🔧 Configuration

### Environment Variables (.env)

```env
GROQ_API_KEY=your_groq_api_key_here
DATABASE_URL=sqlite:///./healNav.db
API_HOST=localhost
API_PORT=8000
FRONTEND_PORT=8501
DEFAULT_SEARCH_RADIUS_KM=50
```

### Groq API Key

1. Visit [Groq Console](https://console.groq.com)
2. Create a free account
3. Generate an API key
4. Add it to your `.env` file

## 🚨 Emergency Features

HealNav automatically detects emergency situations based on keywords like:
- "emergency", "urgent", "severe pain"
- "bleeding", "unconscious", "chest pain"
- "difficulty breathing"

When detected, it will:
- Display emergency alerts
- Recommend immediate medical attention
- Suggest calling emergency services

## 🔌 API Endpoints

- `GET /health` - Health check
- `POST /search` - Search medical facilities
- `GET /doctors` - List all doctors
- `GET /specializations` - Get available specializations
- `POST /analyze` - Analyze medical query

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is open source and available under the MIT License.

## 🆘 Support

If you encounter any issues:

1. Check that all dependencies are installed
2. Verify your Groq API key is correct
3. Ensure both backend and frontend are running
4. Run the test suite to identify problems

For additional help, please create an issue in the repository.

---

**⚠️ Medical Disclaimer**: HealNav is for informational purposes only and should not replace professional medical advice. Always consult with qualified healthcare providers for medical decisions.
