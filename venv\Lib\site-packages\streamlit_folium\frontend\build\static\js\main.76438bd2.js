/*! For license information please see main.76438bd2.js.LICENSE.txt */
(()=>{"use strict";var __webpack_modules__={82:(t,e)=>{var n="function"===typeof Symbol&&Symbol.for,r=n?Symbol.for("react.element"):60103,i=n?Symbol.for("react.portal"):60106,s=n?Symbol.for("react.fragment"):60107,o=n?Symbol.for("react.strict_mode"):60108,a=n?Symbol.for("react.profiler"):60114,l=n?Symbol.for("react.provider"):60109,c=n?Symbol.for("react.context"):60110,u=n?Symbol.for("react.async_mode"):60111,d=n?Symbol.for("react.concurrent_mode"):60111,h=n?Symbol.for("react.forward_ref"):60112,f=n?Symbol.for("react.suspense"):60113,p=n?Symbol.for("react.suspense_list"):60120,y=n?Symbol.for("react.memo"):60115,b=n?Symbol.for("react.lazy"):60116,_=n?Symbol.for("react.block"):60121,m=n?Symbol.for("react.fundamental"):60117,g=n?Symbol.for("react.responder"):60118,v=n?Symbol.for("react.scope"):60119;function w(t){if("object"===typeof t&&null!==t){var e=t.$$typeof;switch(e){case r:switch(t=t.type){case u:case d:case s:case a:case o:case f:return t;default:switch(t=t&&t.$$typeof){case c:case h:case b:case y:case l:return t;default:return e}}case i:return e}}}function I(t){return w(t)===d}e.AsyncMode=u,e.ConcurrentMode=d,e.ContextConsumer=c,e.ContextProvider=l,e.Element=r,e.ForwardRef=h,e.Fragment=s,e.Lazy=b,e.Memo=y,e.Portal=i,e.Profiler=a,e.StrictMode=o,e.Suspense=f,e.isAsyncMode=function(t){return I(t)||w(t)===u},e.isConcurrentMode=I,e.isContextConsumer=function(t){return w(t)===c},e.isContextProvider=function(t){return w(t)===l},e.isElement=function(t){return"object"===typeof t&&null!==t&&t.$$typeof===r},e.isForwardRef=function(t){return w(t)===h},e.isFragment=function(t){return w(t)===s},e.isLazy=function(t){return w(t)===b},e.isMemo=function(t){return w(t)===y},e.isPortal=function(t){return w(t)===i},e.isProfiler=function(t){return w(t)===a},e.isStrictMode=function(t){return w(t)===o},e.isSuspense=function(t){return w(t)===f},e.isValidElementType=function(t){return"string"===typeof t||"function"===typeof t||t===s||t===d||t===a||t===o||t===f||t===p||"object"===typeof t&&null!==t&&(t.$$typeof===b||t.$$typeof===y||t.$$typeof===l||t.$$typeof===c||t.$$typeof===h||t.$$typeof===m||t.$$typeof===g||t.$$typeof===v||t.$$typeof===_)},e.typeOf=w},86:(t,e,n)=>{t.exports=n(82)},123:t=>{var e=Object.getOwnPropertySymbols,n=Object.prototype.hasOwnProperty,r=Object.prototype.propertyIsEnumerable;t.exports=function(){try{if(!Object.assign)return!1;var t=new String("abc");if(t[5]="de","5"===Object.getOwnPropertyNames(t)[0])return!1;for(var e={},n=0;n<10;n++)e["_"+String.fromCharCode(n)]=n;if("0123456789"!==Object.getOwnPropertyNames(e).map((function(t){return e[t]})).join(""))return!1;var r={};return"abcdefghijklmnopqrst".split("").forEach((function(t){r[t]=t})),"abcdefghijklmnopqrst"===Object.keys(Object.assign({},r)).join("")}catch(i){return!1}}()?Object.assign:function(t,i){for(var s,o,a=function(t){if(null===t||void 0===t)throw new TypeError("Object.assign cannot be called with null or undefined");return Object(t)}(t),l=1;l<arguments.length;l++){for(var c in s=Object(arguments[l]))n.call(s,c)&&(a[c]=s[c]);if(e){o=e(s);for(var u=0;u<o.length;u++)r.call(s,o[u])&&(a[o[u]]=s[o[u]])}}return a}},219:(t,e,n)=>{var r=n(86),i={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},s={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},o={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},a={};function l(t){return r.isMemo(t)?o:a[t.$$typeof]||i}a[r.ForwardRef]={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},a[r.Memo]=o;var c=Object.defineProperty,u=Object.getOwnPropertyNames,d=Object.getOwnPropertySymbols,h=Object.getOwnPropertyDescriptor,f=Object.getPrototypeOf,p=Object.prototype;t.exports=function t(e,n,r){if("string"!==typeof n){if(p){var i=f(n);i&&i!==p&&t(e,i,r)}var o=u(n);d&&(o=o.concat(d(n)));for(var a=l(e),y=l(n),b=0;b<o.length;++b){var _=o[b];if(!s[_]&&(!r||!r[_])&&(!y||!y[_])&&(!a||!a[_])){var m=h(n,_);try{c(e,_,m)}catch(g){}}}}return e}},416:(t,e,n)=>{n.d(e,{wk:()=>no});n(219);var r,i,s,o,a,l,c,u,d,h=n(904);!function(t){t[t.V1=0]="V1",t[t.V2=1]="V2",t[t.V3=2]="V3",t[t.V4=3]="V4",t[t.V5=4]="V5"}(r||(r={})),function(t){t[t.Sparse=0]="Sparse",t[t.Dense=1]="Dense"}(i||(i={})),function(t){t[t.HALF=0]="HALF",t[t.SINGLE=1]="SINGLE",t[t.DOUBLE=2]="DOUBLE"}(s||(s={})),function(t){t[t.DAY=0]="DAY",t[t.MILLISECOND=1]="MILLISECOND"}(o||(o={})),function(t){t[t.SECOND=0]="SECOND",t[t.MILLISECOND=1]="MILLISECOND",t[t.MICROSECOND=2]="MICROSECOND",t[t.NANOSECOND=3]="NANOSECOND"}(a||(a={})),function(t){t[t.YEAR_MONTH=0]="YEAR_MONTH",t[t.DAY_TIME=1]="DAY_TIME",t[t.MONTH_DAY_NANO=2]="MONTH_DAY_NANO"}(l||(l={})),function(t){t[t.NONE=0]="NONE",t[t.Schema=1]="Schema",t[t.DictionaryBatch=2]="DictionaryBatch",t[t.RecordBatch=3]="RecordBatch",t[t.Tensor=4]="Tensor",t[t.SparseTensor=5]="SparseTensor"}(c||(c={})),function(t){t[t.NONE=0]="NONE",t[t.Null=1]="Null",t[t.Int=2]="Int",t[t.Float=3]="Float",t[t.Binary=4]="Binary",t[t.Utf8=5]="Utf8",t[t.Bool=6]="Bool",t[t.Decimal=7]="Decimal",t[t.Date=8]="Date",t[t.Time=9]="Time",t[t.Timestamp=10]="Timestamp",t[t.Interval=11]="Interval",t[t.List=12]="List",t[t.Struct=13]="Struct",t[t.Union=14]="Union",t[t.FixedSizeBinary=15]="FixedSizeBinary",t[t.FixedSizeList=16]="FixedSizeList",t[t.Map=17]="Map",t[t.Dictionary=-1]="Dictionary",t[t.Int8=-2]="Int8",t[t.Int16=-3]="Int16",t[t.Int32=-4]="Int32",t[t.Int64=-5]="Int64",t[t.Uint8=-6]="Uint8",t[t.Uint16=-7]="Uint16",t[t.Uint32=-8]="Uint32",t[t.Uint64=-9]="Uint64",t[t.Float16=-10]="Float16",t[t.Float32=-11]="Float32",t[t.Float64=-12]="Float64",t[t.DateDay=-13]="DateDay",t[t.DateMillisecond=-14]="DateMillisecond",t[t.TimestampSecond=-15]="TimestampSecond",t[t.TimestampMillisecond=-16]="TimestampMillisecond",t[t.TimestampMicrosecond=-17]="TimestampMicrosecond",t[t.TimestampNanosecond=-18]="TimestampNanosecond",t[t.TimeSecond=-19]="TimeSecond",t[t.TimeMillisecond=-20]="TimeMillisecond",t[t.TimeMicrosecond=-21]="TimeMicrosecond",t[t.TimeNanosecond=-22]="TimeNanosecond",t[t.DenseUnion=-23]="DenseUnion",t[t.SparseUnion=-24]="SparseUnion",t[t.IntervalDayTime=-25]="IntervalDayTime",t[t.IntervalYearMonth=-26]="IntervalYearMonth"}(u||(u={})),function(t){t[t.OFFSET=0]="OFFSET",t[t.DATA=1]="DATA",t[t.VALIDITY=2]="VALIDITY",t[t.TYPE=3]="TYPE"}(d||(d={}));const[f,p]=(()=>{const t=()=>{throw new Error("BigInt is not available in this environment")};function e(){throw t()}return e.asIntN=()=>{throw t()},e.asUintN=()=>{throw t()},"undefined"!==typeof BigInt?[BigInt,!0]:[e,!1]})(),[y,b]=(()=>{const t=()=>{throw new Error("BigInt64Array is not available in this environment")};return"undefined"!==typeof BigInt64Array?[BigInt64Array,!0]:[class{static get BYTES_PER_ELEMENT(){return 8}static of(){throw t()}static from(){throw t()}constructor(){throw t()}},!1]})(),[_,m]=(()=>{const t=()=>{throw new Error("BigUint64Array is not available in this environment")};return"undefined"!==typeof BigUint64Array?[BigUint64Array,!0]:[class{static get BYTES_PER_ELEMENT(){return 8}static of(){throw t()}static from(){throw t()}constructor(){throw t()}},!1]})(),g=t=>"number"===typeof t,v=t=>"boolean"===typeof t,w=t=>"function"===typeof t,I=t=>null!=t&&Object(t)===t,S=t=>I(t)&&w(t.then),A=t=>I(t)&&w(t[Symbol.iterator]),T=t=>I(t)&&w(t[Symbol.asyncIterator]),O=t=>I(t)&&I(t.schema),B=t=>I(t)&&"done"in t&&"value"in t,D=t=>I(t)&&w(t.stat)&&g(t.fd),M=t=>I(t)&&F(t.body),x=t=>"_getDOMStream"in t&&"_getNodeStream"in t,F=t=>I(t)&&w(t.cancel)&&w(t.getReader)&&!x(t),L=t=>I(t)&&w(t.read)&&w(t.pipe)&&v(t.readable)&&!x(t);function N(t){if(null===t)return"null";if(undefined===t)return"undefined";switch(typeof t){case"number":case"bigint":return`${t}`;case"string":return`"${t}"`}return"function"===typeof t[Symbol.toPrimitive]?t[Symbol.toPrimitive]("string"):ArrayBuffer.isView(t)?t instanceof y||t instanceof _?`[${[...t].map((t=>N(t)))}]`:`[${t}]`:ArrayBuffer.isView(t)?`[${t}]`:JSON.stringify(t,((t,e)=>"bigint"===typeof e?`${e}`:e))}function U(t,e,n,r){return new(n||(n=Promise))((function(i,s){function o(t){try{l(r.next(t))}catch(e){s(e)}}function a(t){try{l(r.throw(t))}catch(e){s(e)}}function l(t){var e;t.done?i(t.value):(e=t.value,e instanceof n?e:new n((function(t){t(e)}))).then(o,a)}l((r=r.apply(t,e||[])).next())}))}Object.create;function C(t){var e="function"===typeof Symbol&&Symbol.iterator,n=e&&t[e],r=0;if(n)return n.call(t);if(t&&"number"===typeof t.length)return{next:function(){return t&&r>=t.length&&(t=void 0),{value:t&&t[r++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")}function E(t){return this instanceof E?(this.v=t,this):new E(t)}function j(t,e,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r,i=n.apply(t,e||[]),s=[];return r=Object.create(("function"===typeof AsyncIterator?AsyncIterator:Object).prototype),o("next"),o("throw"),o("return",(function(t){return function(e){return Promise.resolve(e).then(t,c)}})),r[Symbol.asyncIterator]=function(){return this},r;function o(t,e){i[t]&&(r[t]=function(e){return new Promise((function(n,r){s.push([t,e,n,r])>1||a(t,e)}))},e&&(r[t]=e(r[t])))}function a(t,e){try{(n=i[t](e)).value instanceof E?Promise.resolve(n.value.v).then(l,c):u(s[0][2],n)}catch(r){u(s[0][3],r)}var n}function l(t){a("next",t)}function c(t){a("throw",t)}function u(t,e){t(e),s.shift(),s.length&&a(s[0][0],s[0][1])}}function R(t){var e,n;return e={},r("next"),r("throw",(function(t){throw t})),r("return"),e[Symbol.iterator]=function(){return this},e;function r(r,i){e[r]=t[r]?function(e){return(n=!n)?{value:E(t[r](e)),done:!1}:i?i(e):e}:i}}function P(t){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var e,n=t[Symbol.asyncIterator];return n?n.call(t):(t=C(t),e={},r("next"),r("throw"),r("return"),e[Symbol.asyncIterator]=function(){return this},e);function r(n){e[n]=t[n]&&function(e){return new Promise((function(r,i){(function(t,e,n,r){Promise.resolve(r).then((function(e){t({value:e,done:n})}),e)})(r,i,(e=t[n](e)).done,e.value)}))}}}Object.create;"function"===typeof SuppressedError&&SuppressedError;const k=new TextDecoder("utf-8"),V=t=>k.decode(t),z=new TextEncoder,$=t=>z.encode(t),W="undefined"!==typeof SharedArrayBuffer?SharedArrayBuffer:ArrayBuffer;function Y(t,e){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:e.byteLength;const i=t.byteLength,s=new Uint8Array(t.buffer,t.byteOffset,i),o=new Uint8Array(e.buffer,e.byteOffset,Math.min(r,i));return s.set(o,n),t}function H(t,e){const n=function(t){const e=t[0]?[t[0]]:[];let n,r,i,s;for(let o,a,l=0,c=0,u=t.length;++l<u;)o=e[c],a=t[l],!o||!a||o.buffer!==a.buffer||a.byteOffset<o.byteOffset?a&&(e[++c]=a):(({byteOffset:n,byteLength:i}=o),({byteOffset:r,byteLength:s}=a),n+i<r||r+s<n?a&&(e[++c]=a):e[c]=new Uint8Array(o.buffer,n,r-n+s));return e}(t),r=n.reduce(((t,e)=>t+e.byteLength),0);let i,s,o,a=0,l=-1;const c=Math.min(e||Number.POSITIVE_INFINITY,r);for(const u=n.length;++l<u;){if(i=n[l],s=i.subarray(0,Math.min(i.length,c-a)),c<=a+s.length){s.length<i.length?n[l]=i.subarray(s.length):s.length===i.length&&l++,o?Y(o,s,a):o=s;break}Y(o||(o=new Uint8Array(c)),s,a),a+=s.length}return[o||new Uint8Array(0),n.slice(l),r-(o?o.byteLength:0)]}function K(t,e){let n=B(e)?e.value:e;return n instanceof t?t===Uint8Array?new t(n.buffer,n.byteOffset,n.byteLength):n:n?("string"===typeof n&&(n=$(n)),n instanceof ArrayBuffer||n instanceof W?new t(n):I(r=n)&&w(r.clear)&&w(r.bytes)&&w(r.position)&&w(r.setPosition)&&w(r.capacity)&&w(r.getBufferIdentifier)&&w(r.createLong)?K(t,n.bytes()):ArrayBuffer.isView(n)?n.byteLength<=0?new t(0):new t(n.buffer,n.byteOffset,n.byteLength/t.BYTES_PER_ELEMENT):t.from(n)):new t(0);var r}const G=t=>K(Int32Array,t),J=t=>K(Uint8Array,t),q=t=>(t.next(),t);function*Z(t,e){const n=function*(t){yield t},r="string"===typeof e||ArrayBuffer.isView(e)||e instanceof ArrayBuffer||e instanceof W?n(e):A(e)?e:n(e);return yield*q(function*(e){let n=null;do{n=e.next(yield K(t,n))}while(!n.done)}(r[Symbol.iterator]())),new t}function Q(t,e){return j(this,arguments,(function*(){if(S(e))return yield E(yield E(yield*R(P(Q(t,yield E(e))))));const n=function(t){return j(this,arguments,(function*(){yield yield E(yield E(t))}))},r="string"===typeof e||ArrayBuffer.isView(e)||e instanceof ArrayBuffer||e instanceof W?n(e):A(e)?function(t){return j(this,arguments,(function*(){yield E(yield*R(P(q(function*(t){let e=null;do{e=t.next(yield null===e||void 0===e?void 0:e.value)}while(!e.done)}(t[Symbol.iterator]())))))}))}(e):T(e)?e:n(e);return yield E(yield*R(P(q(function(e){return j(this,arguments,(function*(){let n=null;do{n=yield E(e.next(yield yield E(K(t,n))))}while(!n.done)}))}(r[Symbol.asyncIterator]()))))),yield E(new t)}))}function X(t,e,n){if(0!==t){n=n.slice(0,e+1);for(let r=-1;++r<=e;)n[r]+=t}return n}const tt=Symbol.for("isArrowBigNum");function et(t){for(var e=arguments.length,n=new Array(e>1?e-1:0),r=1;r<e;r++)n[r-1]=arguments[r];return 0===n.length?Object.setPrototypeOf(K(this.TypedArray,t),this.constructor.prototype):Object.setPrototypeOf(new this.TypedArray(t,...n),this.constructor.prototype)}function nt(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];return et.apply(this,e)}function rt(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];return et.apply(this,e)}function it(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];return et.apply(this,e)}function st(t){const{buffer:e,byteOffset:n,length:r,signed:i}=t,s=new _(e,n,r),o=i&&s[s.length-1]&BigInt(1)<<BigInt(63);let a=o?BigInt(1):BigInt(0),l=BigInt(0);if(o){for(const t of s)a+=~t*(BigInt(1)<<BigInt(32)*l++);a*=BigInt(-1)}else for(const c of s)a+=c*(BigInt(1)<<BigInt(32)*l++);return a}let ot,at;function lt(t){let e="";const n=new Uint32Array(2);let r=new Uint16Array(t.buffer,t.byteOffset,t.byteLength/2);const i=new Uint32Array((r=new Uint16Array(r).reverse()).buffer);let s=-1;const o=r.length-1;do{for(n[0]=r[s=0];s<o;)r[s++]=n[1]=n[0]/10,n[0]=(n[0]-10*n[1]<<16)+r[s];r[s]=n[1]=n[0]/10,n[0]=n[0]-10*n[1],e=`${n[0]}${e}`}while(i[0]||i[1]||i[2]||i[3]);return null!==e&&void 0!==e?e:"0"}et.prototype[tt]=!0,et.prototype.toJSON=function(){return`"${ot(this)}"`},et.prototype.valueOf=function(){return st(this)},et.prototype.toString=function(){return ot(this)},et.prototype[Symbol.toPrimitive]=function(){switch(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"default"){case"number":return st(this);case"string":return ot(this);case"default":return at(this)}return ot(this)},Object.setPrototypeOf(nt.prototype,Object.create(Int32Array.prototype)),Object.setPrototypeOf(rt.prototype,Object.create(Uint32Array.prototype)),Object.setPrototypeOf(it.prototype,Object.create(Uint32Array.prototype)),Object.assign(nt.prototype,et.prototype,{constructor:nt,signed:!0,TypedArray:Int32Array,BigIntArray:y}),Object.assign(rt.prototype,et.prototype,{constructor:rt,signed:!1,TypedArray:Uint32Array,BigIntArray:_}),Object.assign(it.prototype,et.prototype,{constructor:it,signed:!0,TypedArray:Uint32Array,BigIntArray:_}),p?(at=t=>8===t.byteLength?new t.BigIntArray(t.buffer,t.byteOffset,1)[0]:lt(t),ot=t=>8===t.byteLength?`${new t.BigIntArray(t.buffer,t.byteOffset,1)[0]}`:lt(t)):(ot=lt,at=ot);class ct{static new(t,e){switch(e){case!0:return new nt(t);case!1:return new rt(t)}switch(t.constructor){case Int8Array:case Int16Array:case Int32Array:case y:return new nt(t)}return 16===t.byteLength?new it(t):new rt(t)}static signed(t){return new nt(t)}static unsigned(t){return new rt(t)}static decimal(t){return new it(t)}constructor(t,e){return ct.new(t,e)}}var ut,dt,ht,ft,pt,yt,bt,_t,mt,gt,vt,wt,It,St,At,Tt,Ot,Bt,Dt,Mt;class xt{static isNull(t){return(null===t||void 0===t?void 0:t.typeId)===u.Null}static isInt(t){return(null===t||void 0===t?void 0:t.typeId)===u.Int}static isFloat(t){return(null===t||void 0===t?void 0:t.typeId)===u.Float}static isBinary(t){return(null===t||void 0===t?void 0:t.typeId)===u.Binary}static isUtf8(t){return(null===t||void 0===t?void 0:t.typeId)===u.Utf8}static isBool(t){return(null===t||void 0===t?void 0:t.typeId)===u.Bool}static isDecimal(t){return(null===t||void 0===t?void 0:t.typeId)===u.Decimal}static isDate(t){return(null===t||void 0===t?void 0:t.typeId)===u.Date}static isTime(t){return(null===t||void 0===t?void 0:t.typeId)===u.Time}static isTimestamp(t){return(null===t||void 0===t?void 0:t.typeId)===u.Timestamp}static isInterval(t){return(null===t||void 0===t?void 0:t.typeId)===u.Interval}static isList(t){return(null===t||void 0===t?void 0:t.typeId)===u.List}static isStruct(t){return(null===t||void 0===t?void 0:t.typeId)===u.Struct}static isUnion(t){return(null===t||void 0===t?void 0:t.typeId)===u.Union}static isFixedSizeBinary(t){return(null===t||void 0===t?void 0:t.typeId)===u.FixedSizeBinary}static isFixedSizeList(t){return(null===t||void 0===t?void 0:t.typeId)===u.FixedSizeList}static isMap(t){return(null===t||void 0===t?void 0:t.typeId)===u.Map}static isDictionary(t){return(null===t||void 0===t?void 0:t.typeId)===u.Dictionary}static isDenseUnion(t){return xt.isUnion(t)&&t.mode===i.Dense}static isSparseUnion(t){return xt.isUnion(t)&&t.mode===i.Sparse}get typeId(){return u.NONE}}ut=Symbol.toStringTag,xt[ut]=((Mt=xt.prototype).children=null,Mt.ArrayType=Array,Mt[Symbol.toStringTag]="DataType");class Ft extends xt{toString(){return"Null"}get typeId(){return u.Null}}dt=Symbol.toStringTag,Ft[dt]=(t=>t[Symbol.toStringTag]="Null")(Ft.prototype);class Lt extends xt{constructor(t,e){super(),this.isSigned=t,this.bitWidth=e}get typeId(){return u.Int}get ArrayType(){switch(this.bitWidth){case 8:return this.isSigned?Int8Array:Uint8Array;case 16:return this.isSigned?Int16Array:Uint16Array;case 32:return this.isSigned?Int32Array:Uint32Array;case 64:return this.isSigned?y:_}throw new Error(`Unrecognized ${this[Symbol.toStringTag]} type`)}toString(){return`${this.isSigned?"I":"Ui"}nt${this.bitWidth}`}}ht=Symbol.toStringTag,Lt[ht]=(t=>(t.isSigned=null,t.bitWidth=null,t[Symbol.toStringTag]="Int"))(Lt.prototype);class Nt extends Lt{constructor(){super(!0,32)}get ArrayType(){return Int32Array}}Object.defineProperty(class extends Lt{constructor(){super(!0,8)}get ArrayType(){return Int8Array}}.prototype,"ArrayType",{value:Int8Array}),Object.defineProperty(class extends Lt{constructor(){super(!0,16)}get ArrayType(){return Int16Array}}.prototype,"ArrayType",{value:Int16Array}),Object.defineProperty(Nt.prototype,"ArrayType",{value:Int32Array}),Object.defineProperty(class extends Lt{constructor(){super(!0,64)}get ArrayType(){return y}}.prototype,"ArrayType",{value:y}),Object.defineProperty(class extends Lt{constructor(){super(!1,8)}get ArrayType(){return Uint8Array}}.prototype,"ArrayType",{value:Uint8Array}),Object.defineProperty(class extends Lt{constructor(){super(!1,16)}get ArrayType(){return Uint16Array}}.prototype,"ArrayType",{value:Uint16Array}),Object.defineProperty(class extends Lt{constructor(){super(!1,32)}get ArrayType(){return Uint32Array}}.prototype,"ArrayType",{value:Uint32Array}),Object.defineProperty(class extends Lt{constructor(){super(!1,64)}get ArrayType(){return _}}.prototype,"ArrayType",{value:_});class Ut extends xt{constructor(t){super(),this.precision=t}get typeId(){return u.Float}get ArrayType(){switch(this.precision){case s.HALF:return Uint16Array;case s.SINGLE:return Float32Array;case s.DOUBLE:return Float64Array}throw new Error(`Unrecognized ${this[Symbol.toStringTag]} type`)}toString(){return`Float${this.precision<<5||16}`}}ft=Symbol.toStringTag,Ut[ft]=(t=>(t.precision=null,t[Symbol.toStringTag]="Float"))(Ut.prototype);Object.defineProperty(class extends Ut{constructor(){super(s.HALF)}}.prototype,"ArrayType",{value:Uint16Array}),Object.defineProperty(class extends Ut{constructor(){super(s.SINGLE)}}.prototype,"ArrayType",{value:Float32Array}),Object.defineProperty(class extends Ut{constructor(){super(s.DOUBLE)}}.prototype,"ArrayType",{value:Float64Array});class Ct extends xt{constructor(){super()}get typeId(){return u.Binary}toString(){return"Binary"}}pt=Symbol.toStringTag,Ct[pt]=(t=>(t.ArrayType=Uint8Array,t[Symbol.toStringTag]="Binary"))(Ct.prototype);class Et extends xt{constructor(){super()}get typeId(){return u.Utf8}toString(){return"Utf8"}}yt=Symbol.toStringTag,Et[yt]=(t=>(t.ArrayType=Uint8Array,t[Symbol.toStringTag]="Utf8"))(Et.prototype);class jt extends xt{constructor(){super()}get typeId(){return u.Bool}toString(){return"Bool"}}bt=Symbol.toStringTag,jt[bt]=(t=>(t.ArrayType=Uint8Array,t[Symbol.toStringTag]="Bool"))(jt.prototype);class Rt extends xt{constructor(t,e){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:128;super(),this.scale=t,this.precision=e,this.bitWidth=n}get typeId(){return u.Decimal}toString(){return`Decimal[${this.precision}e${this.scale>0?"+":""}${this.scale}]`}}_t=Symbol.toStringTag,Rt[_t]=(t=>(t.scale=null,t.precision=null,t.ArrayType=Uint32Array,t[Symbol.toStringTag]="Decimal"))(Rt.prototype);class Pt extends xt{constructor(t){super(),this.unit=t}get typeId(){return u.Date}toString(){return`Date${32*(this.unit+1)}<${o[this.unit]}>`}}mt=Symbol.toStringTag,Pt[mt]=(t=>(t.unit=null,t.ArrayType=Int32Array,t[Symbol.toStringTag]="Date"))(Pt.prototype);class kt extends xt{constructor(t,e){super(),this.unit=t,this.bitWidth=e}get typeId(){return u.Time}toString(){return`Time${this.bitWidth}<${a[this.unit]}>`}get ArrayType(){switch(this.bitWidth){case 32:return Int32Array;case 64:return y}throw new Error(`Unrecognized ${this[Symbol.toStringTag]} type`)}}gt=Symbol.toStringTag,kt[gt]=(t=>(t.unit=null,t.bitWidth=null,t[Symbol.toStringTag]="Time"))(kt.prototype);class Vt extends xt{constructor(t,e){super(),this.unit=t,this.timezone=e}get typeId(){return u.Timestamp}toString(){return`Timestamp<${a[this.unit]}${this.timezone?`, ${this.timezone}`:""}>`}}vt=Symbol.toStringTag,Vt[vt]=(t=>(t.unit=null,t.timezone=null,t.ArrayType=Int32Array,t[Symbol.toStringTag]="Timestamp"))(Vt.prototype);class zt extends xt{constructor(t){super(),this.unit=t}get typeId(){return u.Interval}toString(){return`Interval<${l[this.unit]}>`}}wt=Symbol.toStringTag,zt[wt]=(t=>(t.unit=null,t.ArrayType=Int32Array,t[Symbol.toStringTag]="Interval"))(zt.prototype);class $t extends xt{constructor(t){super(),this.children=[t]}get typeId(){return u.List}toString(){return`List<${this.valueType}>`}get valueType(){return this.children[0].type}get valueField(){return this.children[0]}get ArrayType(){return this.valueType.ArrayType}}It=Symbol.toStringTag,$t[It]=(t=>(t.children=null,t[Symbol.toStringTag]="List"))($t.prototype);class Wt extends xt{constructor(t){super(),this.children=t}get typeId(){return u.Struct}toString(){return`Struct<{${this.children.map((t=>`${t.name}:${t.type}`)).join(", ")}}>`}}St=Symbol.toStringTag,Wt[St]=(t=>(t.children=null,t[Symbol.toStringTag]="Struct"))(Wt.prototype);class Yt extends xt{constructor(t,e,n){super(),this.mode=t,this.children=n,this.typeIds=e=Int32Array.from(e),this.typeIdToChildIndex=e.reduce(((t,e,n)=>(t[e]=n)&&t||t),Object.create(null))}get typeId(){return u.Union}toString(){return`${this[Symbol.toStringTag]}<${this.children.map((t=>`${t.type}`)).join(" | ")}>`}}At=Symbol.toStringTag,Yt[At]=(t=>(t.mode=null,t.typeIds=null,t.children=null,t.typeIdToChildIndex=null,t.ArrayType=Int8Array,t[Symbol.toStringTag]="Union"))(Yt.prototype);class Ht extends xt{constructor(t){super(),this.byteWidth=t}get typeId(){return u.FixedSizeBinary}toString(){return`FixedSizeBinary[${this.byteWidth}]`}}Tt=Symbol.toStringTag,Ht[Tt]=(t=>(t.byteWidth=null,t.ArrayType=Uint8Array,t[Symbol.toStringTag]="FixedSizeBinary"))(Ht.prototype);class Kt extends xt{constructor(t,e){super(),this.listSize=t,this.children=[e]}get typeId(){return u.FixedSizeList}get valueType(){return this.children[0].type}get valueField(){return this.children[0]}get ArrayType(){return this.valueType.ArrayType}toString(){return`FixedSizeList[${this.listSize}]<${this.valueType}>`}}Ot=Symbol.toStringTag,Kt[Ot]=(t=>(t.children=null,t.listSize=null,t[Symbol.toStringTag]="FixedSizeList"))(Kt.prototype);class Gt extends xt{constructor(t){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];super(),this.children=[t],this.keysSorted=e}get typeId(){return u.Map}get keyType(){return this.children[0].type.children[0].type}get valueType(){return this.children[0].type.children[1].type}get childType(){return this.children[0].type}toString(){return`Map<{${this.children[0].type.children.map((t=>`${t.name}:${t.type}`)).join(", ")}}>`}}Bt=Symbol.toStringTag,Gt[Bt]=(t=>(t.children=null,t.keysSorted=null,t[Symbol.toStringTag]="Map_"))(Gt.prototype);const Jt=(qt=-1,()=>++qt);var qt;class Zt extends xt{constructor(t,e,n,r){super(),this.indices=e,this.dictionary=t,this.isOrdered=r||!1,this.id=null==n?Jt():"number"===typeof n?n:n.low}get typeId(){return u.Dictionary}get children(){return this.dictionary.children}get valueType(){return this.dictionary}get ArrayType(){return this.dictionary.ArrayType}toString(){return`Dictionary<${this.indices}, ${this.dictionary}>`}}function Qt(t){const e=t;switch(t.typeId){case u.Decimal:return t.bitWidth/32;case u.Timestamp:return 2;case u.Date:case u.Interval:return 1+e.unit;case u.FixedSizeList:return e.listSize;case u.FixedSizeBinary:return e.byteWidth;default:return 1}}Dt=Symbol.toStringTag,Zt[Dt]=(t=>(t.id=null,t.indices=null,t.isOrdered=null,t.dictionary=null,t[Symbol.toStringTag]="Dictionary"))(Zt.prototype);class Xt{visitMany(t){for(var e=arguments.length,n=new Array(e>1?e-1:0),r=1;r<e;r++)n[r-1]=arguments[r];return t.map(((t,e)=>this.visit(t,...n.map((t=>t[e])))))}visit(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];return this.getVisitFn(e[0],!1).apply(this,e)}getVisitFn(t){return function(t,e){let n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];if("number"===typeof e)return te(t,e,n);if("string"===typeof e&&e in u)return te(t,u[e],n);if(e&&e instanceof xt)return te(t,ee(e),n);if((null===e||void 0===e?void 0:e.type)&&e.type instanceof xt)return te(t,ee(e.type),n);return te(t,u.NONE,n)}(this,t,!(arguments.length>1&&void 0!==arguments[1])||arguments[1])}getVisitFnByTypeId(t){return te(this,t,!(arguments.length>1&&void 0!==arguments[1])||arguments[1])}visitNull(t){return null}visitBool(t){return null}visitInt(t){return null}visitFloat(t){return null}visitUtf8(t){return null}visitBinary(t){return null}visitFixedSizeBinary(t){return null}visitDate(t){return null}visitTimestamp(t){return null}visitTime(t){return null}visitDecimal(t){return null}visitList(t){return null}visitStruct(t){return null}visitUnion(t){return null}visitDictionary(t){return null}visitInterval(t){return null}visitFixedSizeList(t){return null}visitMap(t){return null}}function te(t,e){let n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],r=null;switch(e){case u.Null:r=t.visitNull;break;case u.Bool:r=t.visitBool;break;case u.Int:r=t.visitInt;break;case u.Int8:r=t.visitInt8||t.visitInt;break;case u.Int16:r=t.visitInt16||t.visitInt;break;case u.Int32:r=t.visitInt32||t.visitInt;break;case u.Int64:r=t.visitInt64||t.visitInt;break;case u.Uint8:r=t.visitUint8||t.visitInt;break;case u.Uint16:r=t.visitUint16||t.visitInt;break;case u.Uint32:r=t.visitUint32||t.visitInt;break;case u.Uint64:r=t.visitUint64||t.visitInt;break;case u.Float:r=t.visitFloat;break;case u.Float16:r=t.visitFloat16||t.visitFloat;break;case u.Float32:r=t.visitFloat32||t.visitFloat;break;case u.Float64:r=t.visitFloat64||t.visitFloat;break;case u.Utf8:r=t.visitUtf8;break;case u.Binary:r=t.visitBinary;break;case u.FixedSizeBinary:r=t.visitFixedSizeBinary;break;case u.Date:r=t.visitDate;break;case u.DateDay:r=t.visitDateDay||t.visitDate;break;case u.DateMillisecond:r=t.visitDateMillisecond||t.visitDate;break;case u.Timestamp:r=t.visitTimestamp;break;case u.TimestampSecond:r=t.visitTimestampSecond||t.visitTimestamp;break;case u.TimestampMillisecond:r=t.visitTimestampMillisecond||t.visitTimestamp;break;case u.TimestampMicrosecond:r=t.visitTimestampMicrosecond||t.visitTimestamp;break;case u.TimestampNanosecond:r=t.visitTimestampNanosecond||t.visitTimestamp;break;case u.Time:r=t.visitTime;break;case u.TimeSecond:r=t.visitTimeSecond||t.visitTime;break;case u.TimeMillisecond:r=t.visitTimeMillisecond||t.visitTime;break;case u.TimeMicrosecond:r=t.visitTimeMicrosecond||t.visitTime;break;case u.TimeNanosecond:r=t.visitTimeNanosecond||t.visitTime;break;case u.Decimal:r=t.visitDecimal;break;case u.List:r=t.visitList;break;case u.Struct:r=t.visitStruct;break;case u.Union:r=t.visitUnion;break;case u.DenseUnion:r=t.visitDenseUnion||t.visitUnion;break;case u.SparseUnion:r=t.visitSparseUnion||t.visitUnion;break;case u.Dictionary:r=t.visitDictionary;break;case u.Interval:r=t.visitInterval;break;case u.IntervalDayTime:r=t.visitIntervalDayTime||t.visitInterval;break;case u.IntervalYearMonth:r=t.visitIntervalYearMonth||t.visitInterval;break;case u.FixedSizeList:r=t.visitFixedSizeList;break;case u.Map:r=t.visitMap}if("function"===typeof r)return r;if(!n)return()=>null;throw new Error(`Unrecognized type '${u[e]}'`)}function ee(t){switch(t.typeId){case u.Null:return u.Null;case u.Int:{const{bitWidth:e,isSigned:n}=t;switch(e){case 8:return n?u.Int8:u.Uint8;case 16:return n?u.Int16:u.Uint16;case 32:return n?u.Int32:u.Uint32;case 64:return n?u.Int64:u.Uint64}return u.Int}case u.Float:switch(t.precision){case s.HALF:return u.Float16;case s.SINGLE:return u.Float32;case s.DOUBLE:return u.Float64}return u.Float;case u.Binary:return u.Binary;case u.Utf8:return u.Utf8;case u.Bool:return u.Bool;case u.Decimal:return u.Decimal;case u.Time:switch(t.unit){case a.SECOND:return u.TimeSecond;case a.MILLISECOND:return u.TimeMillisecond;case a.MICROSECOND:return u.TimeMicrosecond;case a.NANOSECOND:return u.TimeNanosecond}return u.Time;case u.Timestamp:switch(t.unit){case a.SECOND:return u.TimestampSecond;case a.MILLISECOND:return u.TimestampMillisecond;case a.MICROSECOND:return u.TimestampMicrosecond;case a.NANOSECOND:return u.TimestampNanosecond}return u.Timestamp;case u.Date:switch(t.unit){case o.DAY:return u.DateDay;case o.MILLISECOND:return u.DateMillisecond}return u.Date;case u.Interval:switch(t.unit){case l.DAY_TIME:return u.IntervalDayTime;case l.YEAR_MONTH:return u.IntervalYearMonth}return u.Interval;case u.Map:return u.Map;case u.List:return u.List;case u.Struct:return u.Struct;case u.Union:switch(t.mode){case i.Dense:return u.DenseUnion;case i.Sparse:return u.SparseUnion}return u.Union;case u.FixedSizeBinary:return u.FixedSizeBinary;case u.FixedSizeList:return u.FixedSizeList;case u.Dictionary:return u.Dictionary}throw new Error(`Unrecognized type '${u[t.typeId]}'`)}Xt.prototype.visitInt8=null,Xt.prototype.visitInt16=null,Xt.prototype.visitInt32=null,Xt.prototype.visitInt64=null,Xt.prototype.visitUint8=null,Xt.prototype.visitUint16=null,Xt.prototype.visitUint32=null,Xt.prototype.visitUint64=null,Xt.prototype.visitFloat16=null,Xt.prototype.visitFloat32=null,Xt.prototype.visitFloat64=null,Xt.prototype.visitDateDay=null,Xt.prototype.visitDateMillisecond=null,Xt.prototype.visitTimestampSecond=null,Xt.prototype.visitTimestampMillisecond=null,Xt.prototype.visitTimestampMicrosecond=null,Xt.prototype.visitTimestampNanosecond=null,Xt.prototype.visitTimeSecond=null,Xt.prototype.visitTimeMillisecond=null,Xt.prototype.visitTimeMicrosecond=null,Xt.prototype.visitTimeNanosecond=null,Xt.prototype.visitDenseUnion=null,Xt.prototype.visitSparseUnion=null,Xt.prototype.visitIntervalDayTime=null,Xt.prototype.visitIntervalYearMonth=null;const ne=new Float64Array(1),re=new Uint32Array(ne.buffer);function ie(t){const e=(31744&t)>>10,n=(1023&t)/1024,r=Math.pow(-1,(32768&t)>>15);switch(e){case 31:return r*(n?Number.NaN:1/0);case 0:return r*(n?6103515625e-14*n:0)}return r*Math.pow(2,e-15)*(1+n)}class se extends Xt{}function oe(t){return(e,n,r)=>{if(e.setValid(n,null!=r))return t(e,n,r)}}const ae=(t,e,n)=>{t[e]=Math.trunc(n%4294967296),t[e+1]=Math.trunc(n/4294967296)},le=(t,e,n,r)=>{if(n+1<e.length){const{[n]:i,[n+1]:s}=e;t.set(r.subarray(0,s-i),i)}},ce=(t,e,n)=>{let{values:r}=t;r[e]=n},ue=(t,e,n)=>{let{values:r}=t;r[e]=n},de=(t,e,n)=>{let{values:r}=t;r[e]=function(t){if(t!==t)return 32256;ne[0]=t;const e=(2147483648&re[1])>>16&65535;let n=2146435072&re[1],r=0;return n>=1089470464?re[0]>0?n=31744:(n=(2080374784&n)>>16,r=(1048575&re[1])>>10):n<=1056964608?(r=1048576+(1048575&re[1]),r=1048576+(r<<(n>>20)-998)>>21,n=0):(n=n-1056964608>>10,r=512+(1048575&re[1])>>10),e|n|65535&r}(n)},he=(t,e,n)=>{let{values:r}=t;((t,e,n)=>{t[e]=Math.trunc(n/864e5)})(r,e,n.valueOf())},fe=(t,e,n)=>{let{values:r}=t;ae(r,2*e,n.valueOf())},pe=(t,e,n)=>{let{values:r}=t;return ae(r,2*e,n/1e3)},ye=(t,e,n)=>{let{values:r}=t;return ae(r,2*e,n)},be=(t,e,n)=>{let{values:r}=t;return((t,e,n)=>{t[e]=Math.trunc(1e3*n%4294967296),t[e+1]=Math.trunc(1e3*n/4294967296)})(r,2*e,n)},_e=(t,e,n)=>{let{values:r}=t;return((t,e,n)=>{t[e]=Math.trunc(1e6*n%4294967296),t[e+1]=Math.trunc(1e6*n/4294967296)})(r,2*e,n)},me=(t,e,n)=>{let{values:r}=t;r[e]=n},ge=(t,e,n)=>{let{values:r}=t;r[e]=n},ve=(t,e,n)=>{let{values:r}=t;r[e]=n},we=(t,e,n)=>{let{values:r}=t;r[e]=n},Ie=(t,e,n)=>{const r=t.type.typeIdToChildIndex[t.typeIds[e]],i=t.children[r];Oe.visit(i,t.valueOffsets[e],n)},Se=(t,e,n)=>{const r=t.type.typeIdToChildIndex[t.typeIds[e]],i=t.children[r];Oe.visit(i,e,n)},Ae=(t,e,n)=>{let{values:r}=t;r.set(n.subarray(0,2),2*e)},Te=(t,e,n)=>{let{values:r}=t;r[e]=12*n[0]+n[1]%12};se.prototype.visitBool=oe(((t,e,n)=>{let{offset:r,values:i}=t;const s=r+e;n?i[s>>3]|=1<<s%8:i[s>>3]&=~(1<<s%8)})),se.prototype.visitInt=oe(ce),se.prototype.visitInt8=oe(ce),se.prototype.visitInt16=oe(ce),se.prototype.visitInt32=oe(ce),se.prototype.visitInt64=oe(ce),se.prototype.visitUint8=oe(ce),se.prototype.visitUint16=oe(ce),se.prototype.visitUint32=oe(ce),se.prototype.visitUint64=oe(ce),se.prototype.visitFloat=oe(((t,e,n)=>{switch(t.type.precision){case s.HALF:return de(t,e,n);case s.SINGLE:case s.DOUBLE:return ue(t,e,n)}})),se.prototype.visitFloat16=oe(de),se.prototype.visitFloat32=oe(ue),se.prototype.visitFloat64=oe(ue),se.prototype.visitUtf8=oe(((t,e,n)=>{let{values:r,valueOffsets:i}=t;le(r,i,e,$(n))})),se.prototype.visitBinary=oe(((t,e,n)=>{let{values:r,valueOffsets:i}=t;return le(r,i,e,n)})),se.prototype.visitFixedSizeBinary=oe(((t,e,n)=>{let{stride:r,values:i}=t;i.set(n.subarray(0,r),r*e)})),se.prototype.visitDate=oe(((t,e,n)=>{t.type.unit===o.DAY?he(t,e,n):fe(t,e,n)})),se.prototype.visitDateDay=oe(he),se.prototype.visitDateMillisecond=oe(fe),se.prototype.visitTimestamp=oe(((t,e,n)=>{switch(t.type.unit){case a.SECOND:return pe(t,e,n);case a.MILLISECOND:return ye(t,e,n);case a.MICROSECOND:return be(t,e,n);case a.NANOSECOND:return _e(t,e,n)}})),se.prototype.visitTimestampSecond=oe(pe),se.prototype.visitTimestampMillisecond=oe(ye),se.prototype.visitTimestampMicrosecond=oe(be),se.prototype.visitTimestampNanosecond=oe(_e),se.prototype.visitTime=oe(((t,e,n)=>{switch(t.type.unit){case a.SECOND:return me(t,e,n);case a.MILLISECOND:return ge(t,e,n);case a.MICROSECOND:return ve(t,e,n);case a.NANOSECOND:return we(t,e,n)}})),se.prototype.visitTimeSecond=oe(me),se.prototype.visitTimeMillisecond=oe(ge),se.prototype.visitTimeMicrosecond=oe(ve),se.prototype.visitTimeNanosecond=oe(we),se.prototype.visitDecimal=oe(((t,e,n)=>{let{values:r,stride:i}=t;r.set(n.subarray(0,i),i*e)})),se.prototype.visitList=oe(((t,e,n)=>{const r=t.children[0],i=t.valueOffsets,s=Oe.getVisitFn(r);if(Array.isArray(n))for(let o=-1,a=i[e],l=i[e+1];a<l;)s(r,a++,n[++o]);else for(let o=-1,a=i[e],l=i[e+1];a<l;)s(r,a++,n.get(++o))})),se.prototype.visitStruct=oe(((t,e,n)=>{const r=t.type.children.map((t=>Oe.getVisitFn(t.type))),i=n instanceof Map?(s=e,o=n,(t,e,n,r)=>e&&t(e,s,o.get(n.name))):n instanceof Wn?((t,e)=>(n,r,i,s)=>r&&n(r,t,e.get(s)))(e,n):Array.isArray(n)?((t,e)=>(n,r,i,s)=>r&&n(r,t,e[s]))(e,n):((t,e)=>(n,r,i,s)=>r&&n(r,t,e[i.name]))(e,n);var s,o;t.type.children.forEach(((e,n)=>i(r[n],t.children[n],e,n)))})),se.prototype.visitUnion=oe(((t,e,n)=>{t.type.mode===i.Dense?Ie(t,e,n):Se(t,e,n)})),se.prototype.visitDenseUnion=oe(Ie),se.prototype.visitSparseUnion=oe(Se),se.prototype.visitDictionary=oe(((t,e,n)=>{var r;null===(r=t.dictionary)||void 0===r||r.set(t.values[e],n)})),se.prototype.visitInterval=oe(((t,e,n)=>{t.type.unit===l.DAY_TIME?Ae(t,e,n):Te(t,e,n)})),se.prototype.visitIntervalDayTime=oe(Ae),se.prototype.visitIntervalYearMonth=oe(Te),se.prototype.visitFixedSizeList=oe(((t,e,n)=>{const{stride:r}=t,i=t.children[0],s=Oe.getVisitFn(i);if(Array.isArray(n))for(let o=-1,a=e*r;++o<r;)s(i,a+o,n[o]);else for(let o=-1,a=e*r;++o<r;)s(i,a+o,n.get(o))})),se.prototype.visitMap=oe(((t,e,n)=>{const r=t.children[0],{valueOffsets:i}=t,s=Oe.getVisitFn(r);let{[e]:o,[e+1]:a}=i;const l=n instanceof Map?n.entries():Object.entries(n);for(const c of l)if(s(r,o,c),++o>=a)break}));const Oe=new se,Be=Symbol.for("parent"),De=Symbol.for("rowIndex");class Me{constructor(t,e){return this[Be]=t,this[De]=e,new Proxy(this,new Fe)}toArray(){return Object.values(this.toJSON())}toJSON(){const t=this[De],e=this[Be],n=e.type.children,r={};for(let i=-1,s=n.length;++i<s;)r[n[i].name]=Xe.visit(e.children[i],t);return r}toString(){return`{${[...this].map((t=>{let[e,n]=t;return`${N(e)}: ${N(n)}`})).join(", ")}}`}[Symbol.for("nodejs.util.inspect.custom")](){return this.toString()}[Symbol.iterator](){return new xe(this[Be],this[De])}}class xe{constructor(t,e){this.childIndex=0,this.children=t.children,this.rowIndex=e,this.childFields=t.type.children,this.numChildren=this.childFields.length}[Symbol.iterator](){return this}next(){const t=this.childIndex;return t<this.numChildren?(this.childIndex=t+1,{done:!1,value:[this.childFields[t].name,Xe.visit(this.children[t],this.rowIndex)]}):{done:!0,value:null}}}Object.defineProperties(Me.prototype,{[Symbol.toStringTag]:{enumerable:!1,configurable:!1,value:"Row"},[Be]:{writable:!0,enumerable:!1,configurable:!1,value:null},[De]:{writable:!0,enumerable:!1,configurable:!1,value:-1}});class Fe{isExtensible(){return!1}deleteProperty(){return!1}preventExtensions(){return!0}ownKeys(t){return t[Be].type.children.map((t=>t.name))}has(t,e){return-1!==t[Be].type.children.findIndex((t=>t.name===e))}getOwnPropertyDescriptor(t,e){if(-1!==t[Be].type.children.findIndex((t=>t.name===e)))return{writable:!0,enumerable:!0,configurable:!0}}get(t,e){if(Reflect.has(t,e))return t[e];const n=t[Be].type.children.findIndex((t=>t.name===e));if(-1!==n){const r=Xe.visit(t[Be].children[n],t[De]);return Reflect.set(t,e,r),r}}set(t,e,n){const r=t[Be].type.children.findIndex((t=>t.name===e));return-1!==r?(Oe.visit(t[Be].children[r],t[De],n),Reflect.set(t,e,n)):!(!Reflect.has(t,e)&&"symbol"!==typeof e)&&Reflect.set(t,e,n)}}class Le extends Xt{}function Ne(t){return(e,n)=>e.getValid(n)?t(e,n):null}const Ue=(t,e)=>4294967296*t[e+1]+(t[e]>>>0),Ce=t=>new Date(t),Ee=(t,e,n)=>{if(n+1>=e.length)return null;const r=e[n],i=e[n+1];return t.subarray(r,i)},je=(t,e)=>{let{values:n}=t;return((t,e)=>Ce(((t,e)=>864e5*t[e])(t,e)))(n,e)},Re=(t,e)=>{let{values:n}=t;return((t,e)=>Ce(Ue(t,e)))(n,2*e)},Pe=(t,e)=>{let{stride:n,values:r}=t;return r[n*e]},ke=(t,e)=>{let{values:n}=t;return n[e]},Ve=(t,e)=>{let{values:n}=t;return 1e3*Ue(n,2*e)},ze=(t,e)=>{let{values:n}=t;return Ue(n,2*e)},$e=(t,e)=>{let{values:n}=t;return((t,e)=>t[e+1]/1e3*4294967296+(t[e]>>>0)/1e3)(n,2*e)},We=(t,e)=>{let{values:n}=t;return((t,e)=>t[e+1]/1e6*4294967296+(t[e]>>>0)/1e6)(n,2*e)},Ye=(t,e)=>{let{values:n}=t;return n[e]},He=(t,e)=>{let{values:n}=t;return n[e]},Ke=(t,e)=>{let{values:n}=t;return n[e]},Ge=(t,e)=>{let{values:n}=t;return n[e]},Je=(t,e)=>{const n=t.type.typeIdToChildIndex[t.typeIds[e]],r=t.children[n];return Xe.visit(r,t.valueOffsets[e])},qe=(t,e)=>{const n=t.type.typeIdToChildIndex[t.typeIds[e]],r=t.children[n];return Xe.visit(r,e)},Ze=(t,e)=>{let{values:n}=t;return n.subarray(2*e,2*(e+1))},Qe=(t,e)=>{let{values:n}=t;const r=n[e],i=new Int32Array(2);return i[0]=Math.trunc(r/12),i[1]=Math.trunc(r%12),i};Le.prototype.visitNull=Ne(((t,e)=>null)),Le.prototype.visitBool=Ne(((t,e)=>{let{offset:n,values:r}=t;const i=n+e;return 0!==(r[i>>3]&1<<i%8)})),Le.prototype.visitInt=Ne(((t,e)=>{let{values:n}=t;return n[e]})),Le.prototype.visitInt8=Ne(Pe),Le.prototype.visitInt16=Ne(Pe),Le.prototype.visitInt32=Ne(Pe),Le.prototype.visitInt64=Ne(ke),Le.prototype.visitUint8=Ne(Pe),Le.prototype.visitUint16=Ne(Pe),Le.prototype.visitUint32=Ne(Pe),Le.prototype.visitUint64=Ne(ke),Le.prototype.visitFloat=Ne(((t,e)=>{let{type:n,values:r}=t;return n.precision!==s.HALF?r[e]:ie(r[e])})),Le.prototype.visitFloat16=Ne(((t,e)=>{let{stride:n,values:r}=t;return ie(r[n*e])})),Le.prototype.visitFloat32=Ne(Pe),Le.prototype.visitFloat64=Ne(Pe),Le.prototype.visitUtf8=Ne(((t,e)=>{let{values:n,valueOffsets:r}=t;const i=Ee(n,r,e);return null!==i?V(i):null})),Le.prototype.visitBinary=Ne(((t,e)=>{let{values:n,valueOffsets:r}=t;return Ee(n,r,e)})),Le.prototype.visitFixedSizeBinary=Ne(((t,e)=>{let{stride:n,values:r}=t;return r.subarray(n*e,n*(e+1))})),Le.prototype.visitDate=Ne(((t,e)=>t.type.unit===o.DAY?je(t,e):Re(t,e))),Le.prototype.visitDateDay=Ne(je),Le.prototype.visitDateMillisecond=Ne(Re),Le.prototype.visitTimestamp=Ne(((t,e)=>{switch(t.type.unit){case a.SECOND:return Ve(t,e);case a.MILLISECOND:return ze(t,e);case a.MICROSECOND:return $e(t,e);case a.NANOSECOND:return We(t,e)}})),Le.prototype.visitTimestampSecond=Ne(Ve),Le.prototype.visitTimestampMillisecond=Ne(ze),Le.prototype.visitTimestampMicrosecond=Ne($e),Le.prototype.visitTimestampNanosecond=Ne(We),Le.prototype.visitTime=Ne(((t,e)=>{switch(t.type.unit){case a.SECOND:return Ye(t,e);case a.MILLISECOND:return He(t,e);case a.MICROSECOND:return Ke(t,e);case a.NANOSECOND:return Ge(t,e)}})),Le.prototype.visitTimeSecond=Ne(Ye),Le.prototype.visitTimeMillisecond=Ne(He),Le.prototype.visitTimeMicrosecond=Ne(Ke),Le.prototype.visitTimeNanosecond=Ne(Ge),Le.prototype.visitDecimal=Ne(((t,e)=>{let{values:n,stride:r}=t;return ct.decimal(n.subarray(r*e,r*(e+1)))})),Le.prototype.visitList=Ne(((t,e)=>{const{valueOffsets:n,stride:r,children:i}=t,{[e*r]:s,[e*r+1]:o}=n,a=i[0].slice(s,o-s);return new Wn([a])})),Le.prototype.visitStruct=Ne(((t,e)=>new Me(t,e))),Le.prototype.visitUnion=Ne(((t,e)=>t.type.mode===i.Dense?Je(t,e):qe(t,e))),Le.prototype.visitDenseUnion=Ne(Je),Le.prototype.visitSparseUnion=Ne(qe),Le.prototype.visitDictionary=Ne(((t,e)=>{var n;return null===(n=t.dictionary)||void 0===n?void 0:n.get(t.values[e])})),Le.prototype.visitInterval=Ne(((t,e)=>t.type.unit===l.DAY_TIME?Ze(t,e):Qe(t,e))),Le.prototype.visitIntervalDayTime=Ne(Ze),Le.prototype.visitIntervalYearMonth=Ne(Qe),Le.prototype.visitFixedSizeList=Ne(((t,e)=>{const{stride:n,children:r}=t,i=r[0].slice(e*n,n);return new Wn([i])})),Le.prototype.visitMap=Ne(((t,e)=>{const{valueOffsets:n,children:r}=t,{[e]:i,[e+1]:s}=n,o=r[0];return new nn(o.slice(i,s-i))}));const Xe=new Le,tn=Symbol.for("keys"),en=Symbol.for("vals");class nn{constructor(t){return this[tn]=new Wn([t.children[0]]).memoize(),this[en]=t.children[1],new Proxy(this,new sn)}[Symbol.iterator](){return new rn(this[tn],this[en])}get size(){return this[tn].length}toArray(){return Object.values(this.toJSON())}toJSON(){const t=this[tn],e=this[en],n={};for(let r=-1,i=t.length;++r<i;)n[t.get(r)]=Xe.visit(e,r);return n}toString(){return`{${[...this].map((t=>{let[e,n]=t;return`${N(e)}: ${N(n)}`})).join(", ")}}`}[Symbol.for("nodejs.util.inspect.custom")](){return this.toString()}}class rn{constructor(t,e){this.keys=t,this.vals=e,this.keyIndex=0,this.numKeys=t.length}[Symbol.iterator](){return this}next(){const t=this.keyIndex;return t===this.numKeys?{done:!0,value:null}:(this.keyIndex++,{done:!1,value:[this.keys.get(t),Xe.visit(this.vals,t)]})}}class sn{isExtensible(){return!1}deleteProperty(){return!1}preventExtensions(){return!0}ownKeys(t){return t[tn].toArray().map(String)}has(t,e){return t[tn].includes(e)}getOwnPropertyDescriptor(t,e){if(-1!==t[tn].indexOf(e))return{writable:!0,enumerable:!0,configurable:!0}}get(t,e){if(Reflect.has(t,e))return t[e];const n=t[tn].indexOf(e);if(-1!==n){const r=Xe.visit(Reflect.get(t,en),n);return Reflect.set(t,e,r),r}}set(t,e,n){const r=t[tn].indexOf(e);return-1!==r?(Oe.visit(Reflect.get(t,en),r,n),Reflect.set(t,e,n)):!!Reflect.has(t,e)&&Reflect.set(t,e,n)}}let on;function an(t,e,n,r){const{length:i=0}=t;let s="number"!==typeof e?0:e,o="number"!==typeof n?i:n;return s<0&&(s=(s%i+i)%i),o<0&&(o=(o%i+i)%i),o<s&&(on=s,s=o,o=on),o>i&&(o=i),r?r(t,s,o):[s,o]}Object.defineProperties(nn.prototype,{[Symbol.toStringTag]:{enumerable:!1,configurable:!1,value:"Row"},[tn]:{writable:!0,enumerable:!1,configurable:!1,value:null},[en]:{writable:!0,enumerable:!1,configurable:!1,value:null}});const ln=t=>t!==t;function cn(t){if("object"!==typeof t||null===t)return ln(t)?ln:e=>e===t;if(t instanceof Date){const e=t.valueOf();return t=>t instanceof Date&&t.valueOf()===e}return ArrayBuffer.isView(t)?e=>!!e&&function(t,e){let n=0;const r=t.length;if(r!==e.length)return!1;if(r>0)do{if(t[n]!==e[n])return!1}while(++n<r);return!0}(t,e):t instanceof Map?function(t){let e=-1;const n=[];for(const r of t.values())n[++e]=cn(r);return un(n)}(t):Array.isArray(t)?function(t){const e=[];for(let n=-1,r=t.length;++n<r;)e[n]=cn(t[n]);return un(e)}(t):t instanceof Wn?function(t){const e=[];for(let n=-1,r=t.length;++n<r;)e[n]=cn(t.get(n));return un(e)}(t):function(t){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];const n=Object.keys(t);if(!e&&0===n.length)return()=>!1;const r=[];for(let i=-1,s=n.length;++i<s;)r[i]=cn(t[n[i]]);return un(r,n)}(t,!0)}function un(t,e){return n=>{if(!n||"object"!==typeof n)return!1;switch(n.constructor){case Array:return function(t,e){const n=t.length;if(e.length!==n)return!1;for(let r=-1;++r<n;)if(!t[r](e[r]))return!1;return!0}(t,n);case Map:return dn(t,n,n.keys());case nn:case Me:case Object:case void 0:return dn(t,n,e||Object.keys(n))}return n instanceof Wn&&function(t,e){const n=t.length;if(e.length!==n)return!1;for(let r=-1;++r<n;)if(!t[r](e.get(r)))return!1;return!0}(t,n)}}function dn(t,e,n){const r=n[Symbol.iterator](),i=e instanceof Map?e.keys():Object.keys(e)[Symbol.iterator](),s=e instanceof Map?e.values():Object.values(e)[Symbol.iterator]();let o=0;const a=t.length;let l=s.next(),c=r.next(),u=i.next();for(;o<a&&!c.done&&!u.done&&!l.done&&(c.value===u.value&&t[o](l.value));++o,c=r.next(),u=i.next(),l=s.next());return!!(o===a&&c.done&&u.done&&l.done)||(r.return&&r.return(),i.return&&i.return(),s.return&&s.return(),!1)}class hn{constructor(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,e=arguments.length>1?arguments[1]:void 0;this.numChunks=t,this.getChunkIterator=e,this.chunkIndex=0,this.chunkIterator=this.getChunkIterator(0)}next(){for(;this.chunkIndex<this.numChunks;){const t=this.chunkIterator.next();if(!t.done)return t;++this.chunkIndex<this.numChunks&&(this.chunkIterator=this.getChunkIterator(this.chunkIndex))}return{done:!0,value:null}}[Symbol.iterator](){return this}}function fn(t){return t.reduce(((t,e)=>t+e.nullCount),0)}function pn(t){return t.reduce(((t,e,n)=>(t[n+1]=t[n]+e.length,t)),new Uint32Array(t.length+1))}function yn(t,e,n,r){const i=[];for(let s=-1,o=t.length;++s<o;){const o=t[s],a=e[s],{length:l}=o;if(a>=r)break;if(n>=a+l)continue;if(a>=n&&a+l<=r){i.push(o);continue}const c=Math.max(0,n-a),u=Math.min(r-a,l);i.push(o.slice(c,u-c))}return 0===i.length&&i.push(t[0].slice(0,0)),i}function bn(t,e,n,r){let i=0,s=0,o=e.length-1;do{if(i>=o-1)return n<e[o]?r(t,i,n-e[i]):null;s=i+Math.trunc(.5*(o-i)),n<e[s]?o=s:i=s}while(i<o)}function _n(t,e){return t.getValid(e)}function mn(t){function e(e,n,r){return t(e[n],r)}return function(t){return bn(this.data,this._offsets,t,e)}}function gn(t){let e;function n(n,r,i){return t(n[r],i,e)}return function(t,r){const i=this.data;e=r;const s=bn(i,this._offsets,t,n);return e=void 0,s}}function vn(t){let e;function n(n,r,i){let s=i,o=0,a=0;for(let l=r-1,c=n.length;++l<c;){const r=n[l];if(~(o=t(r,e,s)))return a+o;s=0,a+=r.length}return-1}return function(t,r){e=t;const i=this.data,s="number"!==typeof r?n(i,0,0):bn(i,this._offsets,r,n);return e=void 0,s}}function wn(t,e,n,r){return 0!==(n&1<<r)}function In(t,e,n,r){return(n&1<<r)>>r}function Sn(t,e,n){const r=n.byteLength+7&-8;if(t>0||n.byteLength<r){const i=new Uint8Array(r);return i.set(t%8===0?n.subarray(t>>3):An(new Tn(n,t,e,null,wn)).subarray(0,r)),i}return n}function An(t){const e=[];let n=0,r=0,i=0;for(const o of t)o&&(i|=1<<r),8===++r&&(e[n++]=i,i=r=0);(0===n||r>0)&&(e[n++]=i);const s=new Uint8Array(e.length+7&-8);return s.set(e),s}class Tn{constructor(t,e,n,r,i){this.bytes=t,this.length=n,this.context=r,this.get=i,this.bit=e%8,this.byteIndex=e>>3,this.byte=t[this.byteIndex++],this.index=0}next(){return this.index<this.length?(8===this.bit&&(this.bit=0,this.byte=this.bytes[this.byteIndex++]),{value:this.get(this.context,this.index++,this.byte,this.bit++)}):{done:!0,value:null}}[Symbol.iterator](){return this}}function On(t,e,n){if(n-e<=0)return 0;if(n-e<8){let r=0;for(const i of new Tn(t,e,n-e,t,In))r+=i;return r}const r=n>>3<<3,i=e+(e%8===0?0:8-e%8);return On(t,e,i)+On(t,r,n)+function(t,e,n){let r=0,i=Math.trunc(e);const s=new DataView(t.buffer,t.byteOffset,t.byteLength),o=void 0===n?t.byteLength:i+n;for(;o-i>=4;)r+=Bn(s.getUint32(i)),i+=4;for(;o-i>=2;)r+=Bn(s.getUint16(i)),i+=2;for(;o-i>=1;)r+=Bn(s.getUint8(i)),i+=1;return r}(t,i>>3,r-i>>3)}function Bn(t){let e=Math.trunc(t);return e-=e>>>1&1431655765,e=(*********&e)+(e>>>2&*********),16843009*(e+(e>>>4)&*********)>>>24}class Dn extends Xt{}function Mn(t,e,n){if(void 0===e)return-1;if(null===e)return function(t,e){const{nullBitmap:n}=t;if(!n||t.nullCount<=0)return-1;let r=0;for(const i of new Tn(n,t.offset+(e||0),t.length,n,wn)){if(!i)return r;++r}return-1}(t,n);const r=Xe.getVisitFn(t),i=cn(e);for(let s=(n||0)-1,o=t.length;++s<o;)if(i(r(t,s)))return s;return-1}function xn(t,e,n){const r=Xe.getVisitFn(t),i=cn(e);for(let s=(n||0)-1,o=t.length;++s<o;)if(i(r(t,s)))return s;return-1}Dn.prototype.visitNull=function(t,e){return null===e&&t.length>0?0:-1},Dn.prototype.visitBool=Mn,Dn.prototype.visitInt=Mn,Dn.prototype.visitInt8=Mn,Dn.prototype.visitInt16=Mn,Dn.prototype.visitInt32=Mn,Dn.prototype.visitInt64=Mn,Dn.prototype.visitUint8=Mn,Dn.prototype.visitUint16=Mn,Dn.prototype.visitUint32=Mn,Dn.prototype.visitUint64=Mn,Dn.prototype.visitFloat=Mn,Dn.prototype.visitFloat16=Mn,Dn.prototype.visitFloat32=Mn,Dn.prototype.visitFloat64=Mn,Dn.prototype.visitUtf8=Mn,Dn.prototype.visitBinary=Mn,Dn.prototype.visitFixedSizeBinary=Mn,Dn.prototype.visitDate=Mn,Dn.prototype.visitDateDay=Mn,Dn.prototype.visitDateMillisecond=Mn,Dn.prototype.visitTimestamp=Mn,Dn.prototype.visitTimestampSecond=Mn,Dn.prototype.visitTimestampMillisecond=Mn,Dn.prototype.visitTimestampMicrosecond=Mn,Dn.prototype.visitTimestampNanosecond=Mn,Dn.prototype.visitTime=Mn,Dn.prototype.visitTimeSecond=Mn,Dn.prototype.visitTimeMillisecond=Mn,Dn.prototype.visitTimeMicrosecond=Mn,Dn.prototype.visitTimeNanosecond=Mn,Dn.prototype.visitDecimal=Mn,Dn.prototype.visitList=Mn,Dn.prototype.visitStruct=Mn,Dn.prototype.visitUnion=Mn,Dn.prototype.visitDenseUnion=xn,Dn.prototype.visitSparseUnion=xn,Dn.prototype.visitDictionary=Mn,Dn.prototype.visitInterval=Mn,Dn.prototype.visitIntervalDayTime=Mn,Dn.prototype.visitIntervalYearMonth=Mn,Dn.prototype.visitFixedSizeList=Mn,Dn.prototype.visitMap=Mn;const Fn=new Dn;class Ln extends Xt{}function Nn(t){const{type:e}=t;if(0===t.nullCount&&1===t.stride&&(e.typeId===u.Timestamp||e instanceof Lt&&64!==e.bitWidth||e instanceof kt&&64!==e.bitWidth||e instanceof Ut&&e.precision!==s.HALF))return new hn(t.data.length,(e=>{const n=t.data[e];return n.values.subarray(0,n.length)[Symbol.iterator]()}));let n=0;return new hn(t.data.length,(e=>{const r=t.data[e].length,i=t.slice(n,n+r);return n+=r,new Un(i)}))}class Un{constructor(t){this.vector=t,this.index=0}next(){return this.index<this.vector.length?{value:this.vector.get(this.index++)}:{done:!0,value:null}}[Symbol.iterator](){return this}}Ln.prototype.visitNull=Nn,Ln.prototype.visitBool=Nn,Ln.prototype.visitInt=Nn,Ln.prototype.visitInt8=Nn,Ln.prototype.visitInt16=Nn,Ln.prototype.visitInt32=Nn,Ln.prototype.visitInt64=Nn,Ln.prototype.visitUint8=Nn,Ln.prototype.visitUint16=Nn,Ln.prototype.visitUint32=Nn,Ln.prototype.visitUint64=Nn,Ln.prototype.visitFloat=Nn,Ln.prototype.visitFloat16=Nn,Ln.prototype.visitFloat32=Nn,Ln.prototype.visitFloat64=Nn,Ln.prototype.visitUtf8=Nn,Ln.prototype.visitBinary=Nn,Ln.prototype.visitFixedSizeBinary=Nn,Ln.prototype.visitDate=Nn,Ln.prototype.visitDateDay=Nn,Ln.prototype.visitDateMillisecond=Nn,Ln.prototype.visitTimestamp=Nn,Ln.prototype.visitTimestampSecond=Nn,Ln.prototype.visitTimestampMillisecond=Nn,Ln.prototype.visitTimestampMicrosecond=Nn,Ln.prototype.visitTimestampNanosecond=Nn,Ln.prototype.visitTime=Nn,Ln.prototype.visitTimeSecond=Nn,Ln.prototype.visitTimeMillisecond=Nn,Ln.prototype.visitTimeMicrosecond=Nn,Ln.prototype.visitTimeNanosecond=Nn,Ln.prototype.visitDecimal=Nn,Ln.prototype.visitList=Nn,Ln.prototype.visitStruct=Nn,Ln.prototype.visitUnion=Nn,Ln.prototype.visitDenseUnion=Nn,Ln.prototype.visitSparseUnion=Nn,Ln.prototype.visitDictionary=Nn,Ln.prototype.visitInterval=Nn,Ln.prototype.visitIntervalDayTime=Nn,Ln.prototype.visitIntervalYearMonth=Nn,Ln.prototype.visitFixedSizeList=Nn,Ln.prototype.visitMap=Nn;const Cn=new Ln,En=(t,e)=>t+e;class jn extends Xt{visitNull(t,e){return 0}visitInt(t,e){return t.type.bitWidth/8}visitFloat(t,e){return t.type.ArrayType.BYTES_PER_ELEMENT}visitBool(t,e){return 1/8}visitDecimal(t,e){return t.type.bitWidth/8}visitDate(t,e){return 4*(t.type.unit+1)}visitTime(t,e){return t.type.bitWidth/8}visitTimestamp(t,e){return t.type.unit===a.SECOND?4:8}visitInterval(t,e){return 4*(t.type.unit+1)}visitStruct(t,e){return t.children.reduce(((t,n)=>t+kn.visit(n,e)),0)}visitFixedSizeBinary(t,e){return t.type.byteWidth}visitMap(t,e){return 8+t.children.reduce(((t,n)=>t+kn.visit(n,e)),0)}visitDictionary(t,e){var n;return t.type.indices.bitWidth/8+((null===(n=t.dictionary)||void 0===n?void 0:n.getByteLength(t.values[e]))||0)}}const Rn=(t,e)=>{let{type:n,children:r,typeIds:i,valueOffsets:s}=t;const o=n.typeIdToChildIndex[i[e]];return 8+kn.visit(r[o],s[e])},Pn=(t,e)=>{let{children:n}=t;return 4+kn.visitMany(n,n.map((()=>e))).reduce(En,0)};jn.prototype.visitUtf8=(t,e)=>{let{valueOffsets:n}=t;return n[e+1]-n[e]+8},jn.prototype.visitBinary=(t,e)=>{let{valueOffsets:n}=t;return n[e+1]-n[e]+8},jn.prototype.visitList=(t,e)=>{let{valueOffsets:n,stride:r,children:i}=t;const s=i[0],{[e*r]:o}=n,{[e*r+1]:a}=n,l=kn.getVisitFn(s.type),c=s.slice(o,a-o);let u=8;for(let d=-1,h=a-o;++d<h;)u+=l(c,d);return u},jn.prototype.visitFixedSizeList=(t,e)=>{let{stride:n,children:r}=t;const i=r[0],s=i.slice(e*n,n),o=kn.getVisitFn(i.type);let a=0;for(let l=-1,c=s.length;++l<c;)a+=o(s,l);return a},jn.prototype.visitUnion=(t,e)=>t.type.mode===i.Dense?Rn(t,e):Pn(t,e),jn.prototype.visitDenseUnion=Rn,jn.prototype.visitSparseUnion=Pn;const kn=new jn;var Vn;const zn={},$n={};class Wn{constructor(t){var e,n,r;const i=t[0]instanceof Wn?t.flatMap((t=>t.data)):t;if(0===i.length||i.some((t=>!(t instanceof Hn))))throw new TypeError("Vector constructor expects an Array of Data instances.");const s=null===(e=i[0])||void 0===e?void 0:e.type;switch(i.length){case 0:this._offsets=[0];break;case 1:{const{get:t,set:e,indexOf:n,byteLength:r}=zn[s.typeId],o=i[0];this.isValid=t=>_n(o,t),this.get=e=>t(o,e),this.set=(t,n)=>e(o,t,n),this.indexOf=t=>n(o,t),this.getByteLength=t=>r(o,t),this._offsets=[0,o.length];break}default:Object.setPrototypeOf(this,$n[s.typeId]),this._offsets=pn(i)}this.data=i,this.type=s,this.stride=Qt(s),this.numChildren=null!==(r=null===(n=s.children)||void 0===n?void 0:n.length)&&void 0!==r?r:0,this.length=this._offsets[this._offsets.length-1]}get byteLength(){return-1===this._byteLength&&(this._byteLength=this.data.reduce(((t,e)=>t+e.byteLength),0)),this._byteLength}get nullCount(){return-1===this._nullCount&&(this._nullCount=fn(this.data)),this._nullCount}get ArrayType(){return this.type.ArrayType}get[Symbol.toStringTag](){return`${this.VectorName}<${this.type[Symbol.toStringTag]}>`}get VectorName(){return`${u[this.type.typeId]}Vector`}isValid(t){return!1}get(t){return null}set(t,e){}indexOf(t,e){return-1}includes(t,e){return this.indexOf(t,e)>0}getByteLength(t){return 0}[Symbol.iterator](){return Cn.visit(this)}concat(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];return new Wn(this.data.concat(e.flatMap((t=>t.data)).flat(Number.POSITIVE_INFINITY)))}slice(t,e){return new Wn(an(this,t,e,((t,e,n)=>{let{data:r,_offsets:i}=t;return yn(r,i,e,n)})))}toJSON(){return[...this]}toArray(){const{type:t,data:e,length:n,stride:r,ArrayType:i}=this;switch(t.typeId){case u.Int:case u.Float:case u.Decimal:case u.Time:case u.Timestamp:switch(e.length){case 0:return new i;case 1:return e[0].values.subarray(0,n*r);default:return e.reduce(((t,e)=>{let{values:n,length:i}=e;return t.array.set(n.subarray(0,i*r),t.offset),t.offset+=i*r,t}),{array:new i(n*r),offset:0}).array}}return[...this]}toString(){return`[${[...this].join(",")}]`}getChild(t){var e;return this.getChildAt(null===(e=this.type.children)||void 0===e?void 0:e.findIndex((e=>e.name===t)))}getChildAt(t){return t>-1&&t<this.numChildren?new Wn(this.data.map((e=>{let{children:n}=e;return n[t]}))):null}get isMemoized(){return!!xt.isDictionary(this.type)&&this.data[0].dictionary.isMemoized}memoize(){if(xt.isDictionary(this.type)){const t=new Yn(this.data[0].dictionary),e=this.data.map((e=>{const n=e.clone();return n.dictionary=t,n}));return new Wn(e)}return new Yn(this)}unmemoize(){if(xt.isDictionary(this.type)&&this.isMemoized){const t=this.data[0].dictionary.unmemoize(),e=this.data.map((e=>{const n=e.clone();return n.dictionary=t,n}));return new Wn(e)}return this}}Vn=Symbol.toStringTag,Wn[Vn]=(t=>{t.type=xt.prototype,t.data=[],t.length=0,t.stride=1,t.numChildren=0,t._nullCount=-1,t._byteLength=-1,t._offsets=new Uint32Array([0]),t[Symbol.isConcatSpreadable]=!0;const e=Object.keys(u).map((t=>u[t])).filter((t=>"number"===typeof t&&t!==u.NONE));for(const n of e){const e=Xe.getVisitFnByTypeId(n),r=Oe.getVisitFnByTypeId(n),i=Fn.getVisitFnByTypeId(n),s=kn.getVisitFnByTypeId(n);zn[n]={get:e,set:r,indexOf:i,byteLength:s},$n[n]=Object.create(t,{isValid:{value:mn(_n)},get:{value:mn(Xe.getVisitFnByTypeId(n))},set:{value:gn(Oe.getVisitFnByTypeId(n))},indexOf:{value:vn(Fn.getVisitFnByTypeId(n))},getByteLength:{value:mn(kn.getVisitFnByTypeId(n))}})}return"Vector"})(Wn.prototype);class Yn extends Wn{constructor(t){super(t.data);const e=this.get,n=this.set,r=this.slice,i=new Array(this.length);Object.defineProperty(this,"get",{value(t){const n=i[t];if(void 0!==n)return n;const r=e.call(this,t);return i[t]=r,r}}),Object.defineProperty(this,"set",{value(t,e){n.call(this,t,e),i[t]=e}}),Object.defineProperty(this,"slice",{value:(t,e)=>new Yn(r.call(this,t,e))}),Object.defineProperty(this,"isMemoized",{value:!0}),Object.defineProperty(this,"unmemoize",{value:()=>new Wn(this.data)}),Object.defineProperty(this,"memoize",{value:()=>this})}}class Hn{constructor(t,e,n,r,i){let s,o=arguments.length>5&&void 0!==arguments[5]?arguments[5]:[],a=arguments.length>6?arguments[6]:void 0;this.type=t,this.children=o,this.dictionary=a,this.offset=Math.floor(Math.max(e||0,0)),this.length=Math.floor(Math.max(n||0,0)),this._nullCount=Math.floor(Math.max(r||0,-1)),i instanceof Hn?(this.stride=i.stride,this.values=i.values,this.typeIds=i.typeIds,this.nullBitmap=i.nullBitmap,this.valueOffsets=i.valueOffsets):(this.stride=Qt(t),i&&((s=i[0])&&(this.valueOffsets=s),(s=i[1])&&(this.values=s),(s=i[2])&&(this.nullBitmap=s),(s=i[3])&&(this.typeIds=s))),this.nullable=0!==this._nullCount&&this.nullBitmap&&this.nullBitmap.byteLength>0}get typeId(){return this.type.typeId}get ArrayType(){return this.type.ArrayType}get buffers(){return[this.valueOffsets,this.values,this.nullBitmap,this.typeIds]}get byteLength(){let t=0;const{valueOffsets:e,values:n,nullBitmap:r,typeIds:i}=this;return e&&(t+=e.byteLength),n&&(t+=n.byteLength),r&&(t+=r.byteLength),i&&(t+=i.byteLength),this.children.reduce(((t,e)=>t+e.byteLength),t)}get nullCount(){let t,e=this._nullCount;return e<=-1&&(t=this.nullBitmap)&&(this._nullCount=e=this.length-On(t,this.offset,this.offset+this.length)),e}getValid(t){if(this.nullable&&this.nullCount>0){const e=this.offset+t;return 0!==(this.nullBitmap[e>>3]&1<<e%8)}return!0}setValid(t,e){if(!this.nullable)return e;if(!this.nullBitmap||this.nullBitmap.byteLength<=t>>3){const{nullBitmap:t}=this._changeLengthAndBackfillNullBitmap(this.length);Object.assign(this,{nullBitmap:t,_nullCount:0})}const{nullBitmap:n,offset:r}=this,i=r+t>>3,s=(r+t)%8,o=n[i]>>s&1;return e?0===o&&(n[i]|=1<<s,this._nullCount=this.nullCount+1):1===o&&(n[i]&=~(1<<s),this._nullCount=this.nullCount-1),e}clone(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.type,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.offset,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this.length,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:this._nullCount,i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:this,s=arguments.length>5&&void 0!==arguments[5]?arguments[5]:this.children;return new Hn(t,e,n,r,i,s,this.dictionary)}slice(t,e){const{stride:n,typeId:r,children:i}=this,s=+(0===this._nullCount)-1,o=16===r?n:1,a=this._sliceBuffers(t,e,n,r);return this.clone(this.type,this.offset+t,e,s,a,0===i.length||this.valueOffsets?i:this._sliceChildren(i,o*t,o*e))}_changeLengthAndBackfillNullBitmap(t){if(this.typeId===u.Null)return this.clone(this.type,0,t,0);const{length:e,nullCount:n}=this,r=new Uint8Array((t+63&-64)>>3).fill(255,0,e>>3);r[e>>3]=(1<<e-(-8&e))-1,n>0&&r.set(Sn(this.offset,e,this.nullBitmap),0);const i=this.buffers;return i[d.VALIDITY]=r,this.clone(this.type,0,t,n+(t-e),i)}_sliceBuffers(t,e,n,r){let i;const{buffers:s}=this;return(i=s[d.TYPE])&&(s[d.TYPE]=i.subarray(t,t+e)),(i=s[d.OFFSET])&&(s[d.OFFSET]=i.subarray(t,t+e+1))||(i=s[d.DATA])&&(s[d.DATA]=6===r?i:i.subarray(n*t,n*(t+e))),s}_sliceChildren(t,e,n){return t.map((t=>t.slice(e,n)))}}Hn.prototype.children=Object.freeze([]);class Kn extends Xt{visit(t){return this.getVisitFn(t.type).call(this,t)}visitNull(t){const{type:e,offset:n=0,length:r=0}=t;return new Hn(e,n,r,0)}visitBool(t){const{type:e,offset:n=0}=t,r=J(t.nullBitmap),i=K(e.ArrayType,t.data),{length:s=i.length>>3,nullCount:o=(t.nullBitmap?-1:0)}=t;return new Hn(e,n,s,o,[void 0,i,r])}visitInt(t){const{type:e,offset:n=0}=t,r=J(t.nullBitmap),i=K(e.ArrayType,t.data),{length:s=i.length,nullCount:o=(t.nullBitmap?-1:0)}=t;return new Hn(e,n,s,o,[void 0,i,r])}visitFloat(t){const{type:e,offset:n=0}=t,r=J(t.nullBitmap),i=K(e.ArrayType,t.data),{length:s=i.length,nullCount:o=(t.nullBitmap?-1:0)}=t;return new Hn(e,n,s,o,[void 0,i,r])}visitUtf8(t){const{type:e,offset:n=0}=t,r=J(t.data),i=J(t.nullBitmap),s=G(t.valueOffsets),{length:o=s.length-1,nullCount:a=(t.nullBitmap?-1:0)}=t;return new Hn(e,n,o,a,[s,r,i])}visitBinary(t){const{type:e,offset:n=0}=t,r=J(t.data),i=J(t.nullBitmap),s=G(t.valueOffsets),{length:o=s.length-1,nullCount:a=(t.nullBitmap?-1:0)}=t;return new Hn(e,n,o,a,[s,r,i])}visitFixedSizeBinary(t){const{type:e,offset:n=0}=t,r=J(t.nullBitmap),i=K(e.ArrayType,t.data),{length:s=i.length/Qt(e),nullCount:o=(t.nullBitmap?-1:0)}=t;return new Hn(e,n,s,o,[void 0,i,r])}visitDate(t){const{type:e,offset:n=0}=t,r=J(t.nullBitmap),i=K(e.ArrayType,t.data),{length:s=i.length/Qt(e),nullCount:o=(t.nullBitmap?-1:0)}=t;return new Hn(e,n,s,o,[void 0,i,r])}visitTimestamp(t){const{type:e,offset:n=0}=t,r=J(t.nullBitmap),i=K(e.ArrayType,t.data),{length:s=i.length/Qt(e),nullCount:o=(t.nullBitmap?-1:0)}=t;return new Hn(e,n,s,o,[void 0,i,r])}visitTime(t){const{type:e,offset:n=0}=t,r=J(t.nullBitmap),i=K(e.ArrayType,t.data),{length:s=i.length/Qt(e),nullCount:o=(t.nullBitmap?-1:0)}=t;return new Hn(e,n,s,o,[void 0,i,r])}visitDecimal(t){const{type:e,offset:n=0}=t,r=J(t.nullBitmap),i=K(e.ArrayType,t.data),{length:s=i.length/Qt(e),nullCount:o=(t.nullBitmap?-1:0)}=t;return new Hn(e,n,s,o,[void 0,i,r])}visitList(t){const{type:e,offset:n=0,child:r}=t,i=J(t.nullBitmap),s=G(t.valueOffsets),{length:o=s.length-1,nullCount:a=(t.nullBitmap?-1:0)}=t;return new Hn(e,n,o,a,[s,void 0,i],[r])}visitStruct(t){const{type:e,offset:n=0,children:r=[]}=t,i=J(t.nullBitmap),{length:s=r.reduce(((t,e)=>{let{length:n}=e;return Math.max(t,n)}),0),nullCount:o=(t.nullBitmap?-1:0)}=t;return new Hn(e,n,s,o,[void 0,void 0,i],r)}visitUnion(t){const{type:e,offset:n=0,children:r=[]}=t,i=J(t.nullBitmap),s=K(e.ArrayType,t.typeIds),{length:o=s.length,nullCount:a=(t.nullBitmap?-1:0)}=t;if(xt.isSparseUnion(e))return new Hn(e,n,o,a,[void 0,void 0,i,s],r);const l=G(t.valueOffsets);return new Hn(e,n,o,a,[l,void 0,i,s],r)}visitDictionary(t){const{type:e,offset:n=0}=t,r=J(t.nullBitmap),i=K(e.indices.ArrayType,t.data),{dictionary:s=new Wn([(new Kn).visit({type:e.dictionary})])}=t,{length:o=i.length,nullCount:a=(t.nullBitmap?-1:0)}=t;return new Hn(e,n,o,a,[void 0,i,r],[],s)}visitInterval(t){const{type:e,offset:n=0}=t,r=J(t.nullBitmap),i=K(e.ArrayType,t.data),{length:s=i.length/Qt(e),nullCount:o=(t.nullBitmap?-1:0)}=t;return new Hn(e,n,s,o,[void 0,i,r])}visitFixedSizeList(t){const{type:e,offset:n=0,child:r=(new Kn).visit({type:e.valueType})}=t,i=J(t.nullBitmap),{length:s=r.length/Qt(e),nullCount:o=(t.nullBitmap?-1:0)}=t;return new Hn(e,n,s,o,[void 0,void 0,i],[r])}visitMap(t){const{type:e,offset:n=0,child:r=(new Kn).visit({type:e.childType})}=t,i=J(t.nullBitmap),s=G(t.valueOffsets),{length:o=s.length-1,nullCount:a=(t.nullBitmap?-1:0)}=t;return new Hn(e,n,o,a,[s,void 0,i],[r])}}function Gn(t){return(new Kn).visit(t)}class Jn{constructor(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],e=arguments.length>1?arguments[1]:void 0,n=arguments.length>2?arguments[2]:void 0;this.fields=t||[],this.metadata=e||new Map,n||(n=Qn(t)),this.dictionaries=n}get[Symbol.toStringTag](){return"Schema"}get names(){return this.fields.map((t=>t.name))}toString(){return`Schema<{ ${this.fields.map(((t,e)=>`${e}: ${t}`)).join(", ")} }>`}select(t){const e=new Set(t),n=this.fields.filter((t=>e.has(t.name)));return new Jn(n,this.metadata)}selectAt(t){const e=t.map((t=>this.fields[t])).filter(Boolean);return new Jn(e,this.metadata)}assign(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];const r=e[0]instanceof Jn?e[0]:Array.isArray(e[0])?new Jn(e[0]):new Jn(e),i=[...this.fields],s=Zn(Zn(new Map,this.metadata),r.metadata),o=r.fields.filter((t=>{const e=i.findIndex((e=>e.name===t.name));return!~e||(i[e]=t.clone({metadata:Zn(Zn(new Map,i[e].metadata),t.metadata)}))&&!1})),a=Qn(o,new Map);return new Jn([...i,...o],s,new Map([...this.dictionaries,...a]))}}Jn.prototype.fields=null,Jn.prototype.metadata=null,Jn.prototype.dictionaries=null;class qn{constructor(t,e){let n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=arguments.length>3?arguments[3]:void 0;this.name=t,this.type=e,this.nullable=n,this.metadata=r||new Map}static new(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];let[r,i,s,o]=e;return e[0]&&"object"===typeof e[0]&&(({name:r}=e[0]),void 0===i&&(i=e[0].type),void 0===s&&(s=e[0].nullable),void 0===o&&(o=e[0].metadata)),new qn(`${r}`,i,s,o)}get typeId(){return this.type.typeId}get[Symbol.toStringTag](){return"Field"}toString(){return`${this.name}: ${this.type}`}clone(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];let[r,i,s,o]=e;return e[0]&&"object"===typeof e[0]?({name:r=this.name,type:i=this.type,nullable:s=this.nullable,metadata:o=this.metadata}=e[0]):[r=this.name,i=this.type,s=this.nullable,o=this.metadata]=e,qn.new(r,i,s,o)}}function Zn(t,e){return new Map([...t||new Map,...e||new Map])}function Qn(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new Map;for(let n=-1,r=t.length;++n<r;){const r=t[n].type;if(xt.isDictionary(r))if(e.has(r.id)){if(e.get(r.id)!==r.dictionary)throw new Error("Cannot create Schema containing two different dictionaries with the same Id")}else e.set(r.id,r.dictionary);r.children&&r.children.length>0&&Qn(r.children,e)}return e}qn.prototype.type=null,qn.prototype.name=null,qn.prototype.nullable=null,qn.prototype.metadata=null;class Xn extends Xt{compareSchemas(t,e){return t===e||e instanceof t.constructor&&this.compareManyFields(t.fields,e.fields)}compareManyFields(t,e){return t===e||Array.isArray(t)&&Array.isArray(e)&&t.length===e.length&&t.every(((t,n)=>this.compareFields(t,e[n])))}compareFields(t,e){return t===e||e instanceof t.constructor&&t.name===e.name&&t.nullable===e.nullable&&this.visit(t.type,e.type)}}function tr(t,e){return e instanceof t.constructor}function er(t,e){return t===e||tr(t,e)}function nr(t,e){return t===e||tr(t,e)&&t.bitWidth===e.bitWidth&&t.isSigned===e.isSigned}function rr(t,e){return t===e||tr(t,e)&&t.precision===e.precision}function ir(t,e){return t===e||tr(t,e)&&t.unit===e.unit}function sr(t,e){return t===e||tr(t,e)&&t.unit===e.unit&&t.timezone===e.timezone}function or(t,e){return t===e||tr(t,e)&&t.unit===e.unit&&t.bitWidth===e.bitWidth}function ar(t,e){return t===e||tr(t,e)&&t.mode===e.mode&&t.typeIds.every(((t,n)=>t===e.typeIds[n]))&&cr.compareManyFields(t.children,e.children)}function lr(t,e){return t===e||tr(t,e)&&t.unit===e.unit}Xn.prototype.visitNull=er,Xn.prototype.visitBool=er,Xn.prototype.visitInt=nr,Xn.prototype.visitInt8=nr,Xn.prototype.visitInt16=nr,Xn.prototype.visitInt32=nr,Xn.prototype.visitInt64=nr,Xn.prototype.visitUint8=nr,Xn.prototype.visitUint16=nr,Xn.prototype.visitUint32=nr,Xn.prototype.visitUint64=nr,Xn.prototype.visitFloat=rr,Xn.prototype.visitFloat16=rr,Xn.prototype.visitFloat32=rr,Xn.prototype.visitFloat64=rr,Xn.prototype.visitUtf8=er,Xn.prototype.visitBinary=er,Xn.prototype.visitFixedSizeBinary=function(t,e){return t===e||tr(t,e)&&t.byteWidth===e.byteWidth},Xn.prototype.visitDate=ir,Xn.prototype.visitDateDay=ir,Xn.prototype.visitDateMillisecond=ir,Xn.prototype.visitTimestamp=sr,Xn.prototype.visitTimestampSecond=sr,Xn.prototype.visitTimestampMillisecond=sr,Xn.prototype.visitTimestampMicrosecond=sr,Xn.prototype.visitTimestampNanosecond=sr,Xn.prototype.visitTime=or,Xn.prototype.visitTimeSecond=or,Xn.prototype.visitTimeMillisecond=or,Xn.prototype.visitTimeMicrosecond=or,Xn.prototype.visitTimeNanosecond=or,Xn.prototype.visitDecimal=er,Xn.prototype.visitList=function(t,e){return t===e||tr(t,e)&&t.children.length===e.children.length&&cr.compareManyFields(t.children,e.children)},Xn.prototype.visitStruct=function(t,e){return t===e||tr(t,e)&&t.children.length===e.children.length&&cr.compareManyFields(t.children,e.children)},Xn.prototype.visitUnion=ar,Xn.prototype.visitDenseUnion=ar,Xn.prototype.visitSparseUnion=ar,Xn.prototype.visitDictionary=function(t,e){return t===e||tr(t,e)&&t.id===e.id&&t.isOrdered===e.isOrdered&&cr.visit(t.indices,e.indices)&&cr.visit(t.dictionary,e.dictionary)},Xn.prototype.visitInterval=lr,Xn.prototype.visitIntervalDayTime=lr,Xn.prototype.visitIntervalYearMonth=lr,Xn.prototype.visitFixedSizeList=function(t,e){return t===e||tr(t,e)&&t.listSize===e.listSize&&t.children.length===e.children.length&&cr.compareManyFields(t.children,e.children)},Xn.prototype.visitMap=function(t,e){return t===e||tr(t,e)&&t.keysSorted===e.keysSorted&&t.children.length===e.children.length&&cr.compareManyFields(t.children,e.children)};const cr=new Xn;function ur(t,e){return cr.compareSchemas(t,e)}var dr,hr;class fr{constructor(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];switch(e.length){case 2:if([this.schema]=e,!(this.schema instanceof Jn))throw new TypeError("RecordBatch constructor expects a [Schema, Data] pair.");if([,this.data=Gn({nullCount:0,type:new Wt(this.schema.fields),children:this.schema.fields.map((t=>Gn({type:t.type,nullCount:0})))})]=e,!(this.data instanceof Hn))throw new TypeError("RecordBatch constructor expects a [Schema, Data] pair.");[this.schema,this.data]=pr(this.schema,this.data.children);break;case 1:{const[t]=e,{fields:n,children:r,length:i}=Object.keys(t).reduce(((e,n,r)=>(e.children[r]=t[n],e.length=Math.max(e.length,t[n].length),e.fields[r]=qn.new({name:n,type:t[n].type,nullable:!0}),e)),{length:0,fields:new Array,children:new Array}),s=new Jn(n),o=Gn({type:new Wt(n),length:i,children:r,nullCount:0});[this.schema,this.data]=pr(s,o.children,i);break}default:throw new TypeError("RecordBatch constructor expects an Object mapping names to child Data, or a [Schema, Data] pair.")}}get dictionaries(){return this._dictionaries||(this._dictionaries=yr(this.schema.fields,this.data.children))}get numCols(){return this.schema.fields.length}get numRows(){return this.data.length}get nullCount(){return this.data.nullCount}isValid(t){return this.data.getValid(t)}get(t){return Xe.visit(this.data,t)}set(t,e){return Oe.visit(this.data,t,e)}indexOf(t,e){return Fn.visit(this.data,t,e)}getByteLength(t){return kn.visit(this.data,t)}[Symbol.iterator](){return Cn.visit(new Wn([this.data]))}toArray(){return[...this]}concat(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];return new gr(this.schema,[this,...e])}slice(t,e){const[n]=new Wn([this.data]).slice(t,e).data;return new fr(this.schema,n)}getChild(t){var e;return this.getChildAt(null===(e=this.schema.fields)||void 0===e?void 0:e.findIndex((e=>e.name===t)))}getChildAt(t){return t>-1&&t<this.schema.fields.length?new Wn([this.data.children[t]]):null}setChild(t,e){var n;return this.setChildAt(null===(n=this.schema.fields)||void 0===n?void 0:n.findIndex((e=>e.name===t)),e)}setChildAt(t,e){let n=this.schema,r=this.data;if(t>-1&&t<this.numCols){e||(e=new Wn([Gn({type:new Ft,length:this.numRows})]));const i=n.fields.slice(),s=r.children.slice(),o=i[t].clone({type:e.type});[i[t],s[t]]=[o,e.data[0]],n=new Jn(i,new Map(this.schema.metadata)),r=Gn({type:new Wt(i),children:s})}return new fr(n,r)}select(t){const e=this.schema.select(t),n=new Wt(e.fields),r=[];for(const i of t){const t=this.schema.fields.findIndex((t=>t.name===i));~t&&(r[t]=this.data.children[t])}return new fr(e,Gn({type:n,length:this.numRows,children:r}))}selectAt(t){const e=this.schema.selectAt(t),n=t.map((t=>this.data.children[t])).filter(Boolean),r=Gn({type:new Wt(e.fields),length:this.numRows,children:n});return new fr(e,r)}}function pr(t,e){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:e.reduce(((t,e)=>Math.max(t,e.length)),0);var r;const i=[...t.fields],s=[...e],o=(n+63&-64)>>3;for(const[a,l]of t.fields.entries()){const t=e[a];t&&t.length===n||(i[a]=l.clone({nullable:!0}),s[a]=null!==(r=null===t||void 0===t?void 0:t._changeLengthAndBackfillNullBitmap(n))&&void 0!==r?r:Gn({type:l.type,length:n,nullCount:n,nullBitmap:new Uint8Array(o)}))}return[t.assign(i),Gn({type:new Wt(i),length:n,children:s})]}function yr(t,e){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:new Map;for(let r=-1,i=t.length;++r<i;){const i=t[r].type,s=e[r];if(xt.isDictionary(i))if(n.has(i.id)){if(n.get(i.id)!==s.dictionary)throw new Error("Cannot create Schema containing two different dictionaries with the same Id")}else s.dictionary&&n.set(i.id,s.dictionary);i.children&&i.children.length>0&&yr(i.children,s.children,n)}return n}dr=Symbol.toStringTag,fr[dr]=(t=>(t._nullCount=-1,t[Symbol.isConcatSpreadable]=!0,"RecordBatch"))(fr.prototype);class br extends fr{constructor(t){const e=t.fields.map((t=>Gn({type:t.type})));super(t,Gn({type:new Wt(t.fields),nullCount:0,children:e}))}}function _r(t,e){return function(t,e){const n=[...t.fields],r=[],i={numBatches:e.reduce(((t,e)=>Math.max(t,e.length)),0)};let s=0,o=0,a=-1;const l=e.length;let c,u=[];for(;i.numBatches-- >0;){for(o=Number.POSITIVE_INFINITY,a=-1;++a<l;)u[a]=c=e[a].shift(),o=Math.min(o,c?c.length:o);Number.isFinite(o)&&(u=mr(n,o,u,e,i),o>0&&(r[s++]=Gn({type:new Wt(n),length:o,nullCount:0,children:u.slice()})))}return[t=t.assign(n),r.map((e=>new fr(t,e)))]}(t,e.map((t=>t.data.concat())))}function mr(t,e,n,r,i){var s;const o=(e+63&-64)>>3;for(let a=-1,l=r.length;++a<l;){const l=n[a],c=null===l||void 0===l?void 0:l.length;if(c>=e)c===e?n[a]=l:(n[a]=l.slice(0,e),i.numBatches=Math.max(i.numBatches,r[a].unshift(l.slice(e,c-e))));else{const r=t[a];t[a]=r.clone({nullable:!0}),n[a]=null!==(s=null===l||void 0===l?void 0:l._changeLengthAndBackfillNullBitmap(e))&&void 0!==s?s:Gn({type:r.type,length:e,nullCount:e,nullBitmap:new Uint8Array(o)})}}return n}class gr{constructor(){for(var t,e,n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];if(0===r.length)return this.batches=[],this.schema=new Jn([]),this._offsets=[0],this;let s,o;r[0]instanceof Jn&&(s=r.shift()),r[r.length-1]instanceof Uint32Array&&(o=r.pop());const a=t=>{if(t){if(t instanceof fr)return[t];if(t instanceof gr)return t.batches;if(t instanceof Hn){if(t.type instanceof Wt)return[new fr(new Jn(t.type.children),t)]}else{if(Array.isArray(t))return t.flatMap((t=>a(t)));if("function"===typeof t[Symbol.iterator])return[...t].flatMap((t=>a(t)));if("object"===typeof t){const e=Object.keys(t),n=e.map((e=>new Wn([t[e]]))),r=new Jn(e.map(((t,e)=>new qn(String(t),n[e].type)))),[,i]=_r(r,n);return 0===i.length?[new fr(t)]:i}}}return[]},l=r.flatMap((t=>a(t)));if(s=null!==(e=null!==s&&void 0!==s?s:null===(t=l[0])||void 0===t?void 0:t.schema)&&void 0!==e?e:new Jn([]),!(s instanceof Jn))throw new TypeError("Table constructor expects a [Schema, RecordBatch[]] pair.");for(const c of l){if(!(c instanceof fr))throw new TypeError("Table constructor expects a [Schema, RecordBatch[]] pair.");if(!ur(s,c.schema))throw new TypeError("Table and inner RecordBatch schemas must be equivalent.")}this.schema=s,this.batches=l,this._offsets=null!==o&&void 0!==o?o:pn(this.data)}get data(){return this.batches.map((t=>{let{data:e}=t;return e}))}get numCols(){return this.schema.fields.length}get numRows(){return this.data.reduce(((t,e)=>t+e.length),0)}get nullCount(){return-1===this._nullCount&&(this._nullCount=fn(this.data)),this._nullCount}isValid(t){return!1}get(t){return null}set(t,e){}indexOf(t,e){return-1}getByteLength(t){return 0}[Symbol.iterator](){return this.batches.length>0?Cn.visit(new Wn(this.data)):new Array(0)[Symbol.iterator]()}toArray(){return[...this]}toString(){return`[\n  ${this.toArray().join(",\n  ")}\n]`}concat(){const t=this.schema;for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];const i=this.data.concat(n.flatMap((t=>{let{data:e}=t;return e})));return new gr(t,i.map((e=>new fr(t,e))))}slice(t,e){const n=this.schema;[t,e]=an({length:this.numRows},t,e);const r=yn(this.data,this._offsets,t,e);return new gr(n,r.map((t=>new fr(n,t))))}getChild(t){return this.getChildAt(this.schema.fields.findIndex((e=>e.name===t)))}getChildAt(t){if(t>-1&&t<this.schema.fields.length){const e=this.data.map((e=>e.children[t]));if(0===e.length){const{type:n}=this.schema.fields[t],r=Gn({type:n,length:0,nullCount:0});e.push(r._changeLengthAndBackfillNullBitmap(this.numRows))}return new Wn(e)}return null}setChild(t,e){var n;return this.setChildAt(null===(n=this.schema.fields)||void 0===n?void 0:n.findIndex((e=>e.name===t)),e)}setChildAt(t,e){let n=this.schema,r=[...this.batches];if(t>-1&&t<this.numCols){e||(e=new Wn([Gn({type:new Ft,length:this.numRows})]));const i=n.fields.slice(),s=i[t].clone({type:e.type}),o=this.schema.fields.map(((t,e)=>this.getChildAt(e)));[i[t],o[t]]=[s,e],[n,r]=_r(n,o)}return new gr(n,r)}select(t){const e=this.schema.fields.reduce(((t,e,n)=>t.set(e.name,n)),new Map);return this.selectAt(t.map((t=>e.get(t))).filter((t=>t>-1)))}selectAt(t){const e=this.schema.selectAt(t),n=this.batches.map((e=>e.selectAt(t)));return new gr(e,n)}assign(t){const e=this.schema.fields,[n,r]=t.schema.fields.reduce(((t,n,r)=>{const[i,s]=t,o=e.findIndex((t=>t.name===n.name));return~o?s[o]=r:i.push(r),t}),[[],[]]),i=this.schema.assign(t.schema),s=[...e.map(((t,e)=>[e,r[e]])).map((e=>{let[n,r]=e;return void 0===r?this.getChildAt(n):t.getChildAt(r)})),...n.map((e=>t.getChildAt(e)))].filter(Boolean);return new gr(..._r(i,s))}}hr=Symbol.toStringTag,gr[hr]=(t=>(t.schema=null,t.batches=[],t._offsets=new Uint32Array([0]),t._nullCount=-1,t[Symbol.isConcatSpreadable]=!0,t.isValid=mn(_n),t.get=mn(Xe.getVisitFn(u.Struct)),t.set=gn(Oe.getVisitFn(u.Struct)),t.indexOf=vn(Fn.getVisitFn(u.Struct)),t.getByteLength=mn(kn.getVisitFn(u.Struct)),"Table"))(gr.prototype);class vr{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}offset(){return this.bb.readInt64(this.bb_pos)}metaDataLength(){return this.bb.readInt32(this.bb_pos+8)}bodyLength(){return this.bb.readInt64(this.bb_pos+16)}static sizeOf(){return 24}static createBlock(t,e,n,r){return t.prep(8,24),t.writeInt64(r),t.pad(4),t.writeInt32(n),t.writeInt64(e),t.offset()}}const wr=new Int32Array(2),Ir=new Float32Array(wr.buffer),Sr=new Float64Array(wr.buffer),Ar=1===new Uint16Array(new Uint8Array([1,0]).buffer)[0];class Tr{constructor(t,e){this.low=0|t,this.high=0|e}static create(t,e){return 0==t&&0==e?Tr.ZERO:new Tr(t,e)}toFloat64(){return(this.low>>>0)+4294967296*this.high}equals(t){return this.low==t.low&&this.high==t.high}}var Or,Br,Dr,Mr,xr;Tr.ZERO=new Tr(0,0),function(t){t[t.UTF8_BYTES=1]="UTF8_BYTES",t[t.UTF16_STRING=2]="UTF16_STRING"}(Or||(Or={}));class Fr{constructor(t){this.bytes_=t,this.position_=0}static allocate(t){return new Fr(new Uint8Array(t))}clear(){this.position_=0}bytes(){return this.bytes_}position(){return this.position_}setPosition(t){this.position_=t}capacity(){return this.bytes_.length}readInt8(t){return this.readUint8(t)<<24>>24}readUint8(t){return this.bytes_[t]}readInt16(t){return this.readUint16(t)<<16>>16}readUint16(t){return this.bytes_[t]|this.bytes_[t+1]<<8}readInt32(t){return this.bytes_[t]|this.bytes_[t+1]<<8|this.bytes_[t+2]<<16|this.bytes_[t+3]<<24}readUint32(t){return this.readInt32(t)>>>0}readInt64(t){return new Tr(this.readInt32(t),this.readInt32(t+4))}readUint64(t){return new Tr(this.readUint32(t),this.readUint32(t+4))}readFloat32(t){return wr[0]=this.readInt32(t),Ir[0]}readFloat64(t){return wr[Ar?0:1]=this.readInt32(t),wr[Ar?1:0]=this.readInt32(t+4),Sr[0]}writeInt8(t,e){this.bytes_[t]=e}writeUint8(t,e){this.bytes_[t]=e}writeInt16(t,e){this.bytes_[t]=e,this.bytes_[t+1]=e>>8}writeUint16(t,e){this.bytes_[t]=e,this.bytes_[t+1]=e>>8}writeInt32(t,e){this.bytes_[t]=e,this.bytes_[t+1]=e>>8,this.bytes_[t+2]=e>>16,this.bytes_[t+3]=e>>24}writeUint32(t,e){this.bytes_[t]=e,this.bytes_[t+1]=e>>8,this.bytes_[t+2]=e>>16,this.bytes_[t+3]=e>>24}writeInt64(t,e){this.writeInt32(t,e.low),this.writeInt32(t+4,e.high)}writeUint64(t,e){this.writeUint32(t,e.low),this.writeUint32(t+4,e.high)}writeFloat32(t,e){Ir[0]=e,this.writeInt32(t,wr[0])}writeFloat64(t,e){Sr[0]=e,this.writeInt32(t,wr[Ar?0:1]),this.writeInt32(t+4,wr[Ar?1:0])}getBufferIdentifier(){if(this.bytes_.length<this.position_+4+4)throw new Error("FlatBuffers: ByteBuffer is too short to contain an identifier.");let t="";for(let e=0;e<4;e++)t+=String.fromCharCode(this.readInt8(this.position_+4+e));return t}__offset(t,e){const n=t-this.readInt32(t);return e<this.readInt16(n)?this.readInt16(n+e):0}__union(t,e){return t.bb_pos=e+this.readInt32(e),t.bb=this,t}__string(t,e){t+=this.readInt32(t);const n=this.readInt32(t);let r="",i=0;if(t+=4,e===Or.UTF8_BYTES)return this.bytes_.subarray(t,t+n);for(;i<n;){let e;const n=this.readUint8(t+i++);if(n<192)e=n;else{const r=this.readUint8(t+i++);if(n<224)e=(31&n)<<6|63&r;else{const s=this.readUint8(t+i++);if(n<240)e=(15&n)<<12|(63&r)<<6|63&s;else{e=(7&n)<<18|(63&r)<<12|(63&s)<<6|63&this.readUint8(t+i++)}}}e<65536?r+=String.fromCharCode(e):(e-=65536,r+=String.fromCharCode(55296+(e>>10),56320+(1023&e)))}return r}__union_with_string(t,e){return"string"===typeof t?this.__string(e):this.__union(t,e)}__indirect(t){return t+this.readInt32(t)}__vector(t){return t+this.readInt32(t)+4}__vector_len(t){return this.readInt32(t+this.readInt32(t))}__has_identifier(t){if(4!=t.length)throw new Error("FlatBuffers: file identifier must be length 4");for(let e=0;e<4;e++)if(t.charCodeAt(e)!=this.readInt8(this.position()+4+e))return!1;return!0}createLong(t,e){return Tr.create(t,e)}createScalarList(t,e){const n=[];for(let r=0;r<e;++r)null!==t(r)&&n.push(t(r));return n}createObjList(t,e){const n=[];for(let r=0;r<e;++r){const e=t(r);null!==e&&n.push(e.unpack())}return n}}class Lr{constructor(t){let e;this.minalign=1,this.vtable=null,this.vtable_in_use=0,this.isNested=!1,this.object_start=0,this.vtables=[],this.vector_num_elems=0,this.force_defaults=!1,this.string_maps=null,e=t||1024,this.bb=Fr.allocate(e),this.space=e}clear(){this.bb.clear(),this.space=this.bb.capacity(),this.minalign=1,this.vtable=null,this.vtable_in_use=0,this.isNested=!1,this.object_start=0,this.vtables=[],this.vector_num_elems=0,this.force_defaults=!1,this.string_maps=null}forceDefaults(t){this.force_defaults=t}dataBuffer(){return this.bb}asUint8Array(){return this.bb.bytes().subarray(this.bb.position(),this.bb.position()+this.offset())}prep(t,e){t>this.minalign&&(this.minalign=t);const n=1+~(this.bb.capacity()-this.space+e)&t-1;for(;this.space<n+t+e;){const t=this.bb.capacity();this.bb=Lr.growByteBuffer(this.bb),this.space+=this.bb.capacity()-t}this.pad(n)}pad(t){for(let e=0;e<t;e++)this.bb.writeInt8(--this.space,0)}writeInt8(t){this.bb.writeInt8(this.space-=1,t)}writeInt16(t){this.bb.writeInt16(this.space-=2,t)}writeInt32(t){this.bb.writeInt32(this.space-=4,t)}writeInt64(t){this.bb.writeInt64(this.space-=8,t)}writeFloat32(t){this.bb.writeFloat32(this.space-=4,t)}writeFloat64(t){this.bb.writeFloat64(this.space-=8,t)}addInt8(t){this.prep(1,0),this.writeInt8(t)}addInt16(t){this.prep(2,0),this.writeInt16(t)}addInt32(t){this.prep(4,0),this.writeInt32(t)}addInt64(t){this.prep(8,0),this.writeInt64(t)}addFloat32(t){this.prep(4,0),this.writeFloat32(t)}addFloat64(t){this.prep(8,0),this.writeFloat64(t)}addFieldInt8(t,e,n){(this.force_defaults||e!=n)&&(this.addInt8(e),this.slot(t))}addFieldInt16(t,e,n){(this.force_defaults||e!=n)&&(this.addInt16(e),this.slot(t))}addFieldInt32(t,e,n){(this.force_defaults||e!=n)&&(this.addInt32(e),this.slot(t))}addFieldInt64(t,e,n){!this.force_defaults&&e.equals(n)||(this.addInt64(e),this.slot(t))}addFieldFloat32(t,e,n){(this.force_defaults||e!=n)&&(this.addFloat32(e),this.slot(t))}addFieldFloat64(t,e,n){(this.force_defaults||e!=n)&&(this.addFloat64(e),this.slot(t))}addFieldOffset(t,e,n){(this.force_defaults||e!=n)&&(this.addOffset(e),this.slot(t))}addFieldStruct(t,e,n){e!=n&&(this.nested(e),this.slot(t))}nested(t){if(t!=this.offset())throw new Error("FlatBuffers: struct must be serialized inline.")}notNested(){if(this.isNested)throw new Error("FlatBuffers: object serialization must not be nested.")}slot(t){null!==this.vtable&&(this.vtable[t]=this.offset())}offset(){return this.bb.capacity()-this.space}static growByteBuffer(t){const e=t.capacity();if(3221225472&e)throw new Error("FlatBuffers: cannot grow buffer beyond 2 gigabytes.");const n=e<<1,r=Fr.allocate(n);return r.setPosition(n-e),r.bytes().set(t.bytes(),n-e),r}addOffset(t){this.prep(4,0),this.writeInt32(this.offset()-t+4)}startObject(t){this.notNested(),null==this.vtable&&(this.vtable=[]),this.vtable_in_use=t;for(let e=0;e<t;e++)this.vtable[e]=0;this.isNested=!0,this.object_start=this.offset()}endObject(){if(null==this.vtable||!this.isNested)throw new Error("FlatBuffers: endObject called without startObject");this.addInt32(0);const t=this.offset();let e=this.vtable_in_use-1;for(;e>=0&&0==this.vtable[e];e--);const n=e+1;for(;e>=0;e--)this.addInt16(0!=this.vtable[e]?t-this.vtable[e]:0);this.addInt16(t-this.object_start);const r=2*(n+2);this.addInt16(r);let i=0;const s=this.space;t:for(e=0;e<this.vtables.length;e++){const t=this.bb.capacity()-this.vtables[e];if(r==this.bb.readInt16(t)){for(let e=2;e<r;e+=2)if(this.bb.readInt16(s+e)!=this.bb.readInt16(t+e))continue t;i=this.vtables[e];break}}return i?(this.space=this.bb.capacity()-t,this.bb.writeInt32(this.space,i-t)):(this.vtables.push(this.offset()),this.bb.writeInt32(this.bb.capacity()-t,this.offset()-t)),this.isNested=!1,t}finish(t,e,n){const r=n?4:0;if(e){const t=e;if(this.prep(this.minalign,8+r),4!=t.length)throw new Error("FlatBuffers: file identifier must be length 4");for(let e=3;e>=0;e--)this.writeInt8(t.charCodeAt(e))}this.prep(this.minalign,4+r),this.addOffset(t),r&&this.addInt32(this.bb.capacity()-this.space),this.bb.setPosition(this.space)}finishSizePrefixed(t,e){this.finish(t,e,!0)}requiredField(t,e){const n=this.bb.capacity()-t,r=n-this.bb.readInt32(n);if(!(0!=this.bb.readInt16(r+e)))throw new Error("FlatBuffers: field "+e+" must be set")}startVector(t,e,n){this.notNested(),this.vector_num_elems=e,this.prep(4,t*e),this.prep(n,t*e)}endVector(){return this.writeInt32(this.vector_num_elems),this.offset()}createSharedString(t){if(!t)return 0;if(this.string_maps||(this.string_maps=new Map),this.string_maps.has(t))return this.string_maps.get(t);const e=this.createString(t);return this.string_maps.set(t,e),e}createString(t){if(!t)return 0;let e;if(t instanceof Uint8Array)e=t;else{e=[];let n=0;for(;n<t.length;){let r;const i=t.charCodeAt(n++);if(i<55296||i>=56320)r=i;else{r=(i<<10)+t.charCodeAt(n++)+-56613888}r<128?e.push(r):(r<2048?e.push(r>>6&31|192):(r<65536?e.push(r>>12&15|224):e.push(r>>18&7|240,r>>12&63|128),e.push(r>>6&63|128)),e.push(63&r|128))}}this.addInt8(0),this.startVector(1,e.length,1),this.bb.setPosition(this.space-=e.length);for(let n=0,r=this.space,i=this.bb.bytes();n<e.length;n++)i[r++]=e[n];return this.endVector()}createLong(t,e){return Tr.create(t,e)}createObjectOffset(t){return null===t?0:"string"===typeof t?this.createString(t):t.pack(this)}createObjectOffsetList(t){const e=[];for(let n=0;n<t.length;++n){const r=t[n];if(null===r)throw new Error("FlatBuffers: Argument for createObjectOffsetList cannot contain null.");e.push(this.createObjectOffset(r))}return e}createStructOffsetList(t,e){return e(this,t.length),this.createObjectOffsetList(t),this.endVector()}}class Nr{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsKeyValue(t,e){return(e||new Nr).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsKeyValue(t,e){return t.setPosition(t.position()+4),(e||new Nr).__init(t.readInt32(t.position())+t.position(),t)}key(t){const e=this.bb.__offset(this.bb_pos,4);return e?this.bb.__string(this.bb_pos+e,t):null}value(t){const e=this.bb.__offset(this.bb_pos,6);return e?this.bb.__string(this.bb_pos+e,t):null}static startKeyValue(t){t.startObject(2)}static addKey(t,e){t.addFieldOffset(0,e,0)}static addValue(t,e){t.addFieldOffset(1,e,0)}static endKeyValue(t){return t.endObject()}static createKeyValue(t,e,n){return Nr.startKeyValue(t),Nr.addKey(t,e),Nr.addValue(t,n),Nr.endKeyValue(t)}}!function(t){t[t.V1=0]="V1",t[t.V2=1]="V2",t[t.V3=2]="V3",t[t.V4=3]="V4",t[t.V5=4]="V5"}(Br||(Br={})),function(t){t[t.Little=0]="Little",t[t.Big=1]="Big"}(Dr||(Dr={})),function(t){t[t.DenseArray=0]="DenseArray"}(Mr||(Mr={}));class Ur{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsInt(t,e){return(e||new Ur).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsInt(t,e){return t.setPosition(t.position()+4),(e||new Ur).__init(t.readInt32(t.position())+t.position(),t)}bitWidth(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt32(this.bb_pos+t):0}isSigned(){const t=this.bb.__offset(this.bb_pos,6);return!!t&&!!this.bb.readInt8(this.bb_pos+t)}static startInt(t){t.startObject(2)}static addBitWidth(t,e){t.addFieldInt32(0,e,0)}static addIsSigned(t,e){t.addFieldInt8(1,+e,0)}static endInt(t){return t.endObject()}static createInt(t,e,n){return Ur.startInt(t),Ur.addBitWidth(t,e),Ur.addIsSigned(t,n),Ur.endInt(t)}}class Cr{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsDictionaryEncoding(t,e){return(e||new Cr).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsDictionaryEncoding(t,e){return t.setPosition(t.position()+4),(e||new Cr).__init(t.readInt32(t.position())+t.position(),t)}id(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt64(this.bb_pos+t):this.bb.createLong(0,0)}indexType(t){const e=this.bb.__offset(this.bb_pos,6);return e?(t||new Ur).__init(this.bb.__indirect(this.bb_pos+e),this.bb):null}isOrdered(){const t=this.bb.__offset(this.bb_pos,8);return!!t&&!!this.bb.readInt8(this.bb_pos+t)}dictionaryKind(){const t=this.bb.__offset(this.bb_pos,10);return t?this.bb.readInt16(this.bb_pos+t):Mr.DenseArray}static startDictionaryEncoding(t){t.startObject(4)}static addId(t,e){t.addFieldInt64(0,e,t.createLong(0,0))}static addIndexType(t,e){t.addFieldOffset(1,e,0)}static addIsOrdered(t,e){t.addFieldInt8(2,+e,0)}static addDictionaryKind(t,e){t.addFieldInt16(3,e,Mr.DenseArray)}static endDictionaryEncoding(t){return t.endObject()}}!function(t){t[t.NONE=0]="NONE",t[t.Null=1]="Null",t[t.Int=2]="Int",t[t.FloatingPoint=3]="FloatingPoint",t[t.Binary=4]="Binary",t[t.Utf8=5]="Utf8",t[t.Bool=6]="Bool",t[t.Decimal=7]="Decimal",t[t.Date=8]="Date",t[t.Time=9]="Time",t[t.Timestamp=10]="Timestamp",t[t.Interval=11]="Interval",t[t.List=12]="List",t[t.Struct_=13]="Struct_",t[t.Union=14]="Union",t[t.FixedSizeBinary=15]="FixedSizeBinary",t[t.FixedSizeList=16]="FixedSizeList",t[t.Map=17]="Map",t[t.Duration=18]="Duration",t[t.LargeBinary=19]="LargeBinary",t[t.LargeUtf8=20]="LargeUtf8",t[t.LargeList=21]="LargeList"}(xr||(xr={}));class Er{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsField(t,e){return(e||new Er).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsField(t,e){return t.setPosition(t.position()+4),(e||new Er).__init(t.readInt32(t.position())+t.position(),t)}name(t){const e=this.bb.__offset(this.bb_pos,4);return e?this.bb.__string(this.bb_pos+e,t):null}nullable(){const t=this.bb.__offset(this.bb_pos,6);return!!t&&!!this.bb.readInt8(this.bb_pos+t)}typeType(){const t=this.bb.__offset(this.bb_pos,8);return t?this.bb.readUint8(this.bb_pos+t):xr.NONE}type(t){const e=this.bb.__offset(this.bb_pos,10);return e?this.bb.__union(t,this.bb_pos+e):null}dictionary(t){const e=this.bb.__offset(this.bb_pos,12);return e?(t||new Cr).__init(this.bb.__indirect(this.bb_pos+e),this.bb):null}children(t,e){const n=this.bb.__offset(this.bb_pos,14);return n?(e||new Er).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+n)+4*t),this.bb):null}childrenLength(){const t=this.bb.__offset(this.bb_pos,14);return t?this.bb.__vector_len(this.bb_pos+t):0}customMetadata(t,e){const n=this.bb.__offset(this.bb_pos,16);return n?(e||new Nr).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+n)+4*t),this.bb):null}customMetadataLength(){const t=this.bb.__offset(this.bb_pos,16);return t?this.bb.__vector_len(this.bb_pos+t):0}static startField(t){t.startObject(7)}static addName(t,e){t.addFieldOffset(0,e,0)}static addNullable(t,e){t.addFieldInt8(1,+e,0)}static addTypeType(t,e){t.addFieldInt8(2,e,xr.NONE)}static addType(t,e){t.addFieldOffset(3,e,0)}static addDictionary(t,e){t.addFieldOffset(4,e,0)}static addChildren(t,e){t.addFieldOffset(5,e,0)}static createChildrenVector(t,e){t.startVector(4,e.length,4);for(let n=e.length-1;n>=0;n--)t.addOffset(e[n]);return t.endVector()}static startChildrenVector(t,e){t.startVector(4,e,4)}static addCustomMetadata(t,e){t.addFieldOffset(6,e,0)}static createCustomMetadataVector(t,e){t.startVector(4,e.length,4);for(let n=e.length-1;n>=0;n--)t.addOffset(e[n]);return t.endVector()}static startCustomMetadataVector(t,e){t.startVector(4,e,4)}static endField(t){return t.endObject()}}class jr{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsSchema(t,e){return(e||new jr).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsSchema(t,e){return t.setPosition(t.position()+4),(e||new jr).__init(t.readInt32(t.position())+t.position(),t)}endianness(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):Dr.Little}fields(t,e){const n=this.bb.__offset(this.bb_pos,6);return n?(e||new Er).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+n)+4*t),this.bb):null}fieldsLength(){const t=this.bb.__offset(this.bb_pos,6);return t?this.bb.__vector_len(this.bb_pos+t):0}customMetadata(t,e){const n=this.bb.__offset(this.bb_pos,8);return n?(e||new Nr).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+n)+4*t),this.bb):null}customMetadataLength(){const t=this.bb.__offset(this.bb_pos,8);return t?this.bb.__vector_len(this.bb_pos+t):0}features(t){const e=this.bb.__offset(this.bb_pos,10);return e?this.bb.readInt64(this.bb.__vector(this.bb_pos+e)+8*t):this.bb.createLong(0,0)}featuresLength(){const t=this.bb.__offset(this.bb_pos,10);return t?this.bb.__vector_len(this.bb_pos+t):0}static startSchema(t){t.startObject(4)}static addEndianness(t,e){t.addFieldInt16(0,e,Dr.Little)}static addFields(t,e){t.addFieldOffset(1,e,0)}static createFieldsVector(t,e){t.startVector(4,e.length,4);for(let n=e.length-1;n>=0;n--)t.addOffset(e[n]);return t.endVector()}static startFieldsVector(t,e){t.startVector(4,e,4)}static addCustomMetadata(t,e){t.addFieldOffset(2,e,0)}static createCustomMetadataVector(t,e){t.startVector(4,e.length,4);for(let n=e.length-1;n>=0;n--)t.addOffset(e[n]);return t.endVector()}static startCustomMetadataVector(t,e){t.startVector(4,e,4)}static addFeatures(t,e){t.addFieldOffset(3,e,0)}static createFeaturesVector(t,e){t.startVector(8,e.length,8);for(let n=e.length-1;n>=0;n--)t.addInt64(e[n]);return t.endVector()}static startFeaturesVector(t,e){t.startVector(8,e,8)}static endSchema(t){return t.endObject()}static finishSchemaBuffer(t,e){t.finish(e)}static finishSizePrefixedSchemaBuffer(t,e){t.finish(e,void 0,!0)}static createSchema(t,e,n,r,i){return jr.startSchema(t),jr.addEndianness(t,e),jr.addFields(t,n),jr.addCustomMetadata(t,r),jr.addFeatures(t,i),jr.endSchema(t)}}class Rr{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsFooter(t,e){return(e||new Rr).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsFooter(t,e){return t.setPosition(t.position()+4),(e||new Rr).__init(t.readInt32(t.position())+t.position(),t)}version(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):Br.V1}schema(t){const e=this.bb.__offset(this.bb_pos,6);return e?(t||new jr).__init(this.bb.__indirect(this.bb_pos+e),this.bb):null}dictionaries(t,e){const n=this.bb.__offset(this.bb_pos,8);return n?(e||new vr).__init(this.bb.__vector(this.bb_pos+n)+24*t,this.bb):null}dictionariesLength(){const t=this.bb.__offset(this.bb_pos,8);return t?this.bb.__vector_len(this.bb_pos+t):0}recordBatches(t,e){const n=this.bb.__offset(this.bb_pos,10);return n?(e||new vr).__init(this.bb.__vector(this.bb_pos+n)+24*t,this.bb):null}recordBatchesLength(){const t=this.bb.__offset(this.bb_pos,10);return t?this.bb.__vector_len(this.bb_pos+t):0}customMetadata(t,e){const n=this.bb.__offset(this.bb_pos,12);return n?(e||new Nr).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+n)+4*t),this.bb):null}customMetadataLength(){const t=this.bb.__offset(this.bb_pos,12);return t?this.bb.__vector_len(this.bb_pos+t):0}static startFooter(t){t.startObject(5)}static addVersion(t,e){t.addFieldInt16(0,e,Br.V1)}static addSchema(t,e){t.addFieldOffset(1,e,0)}static addDictionaries(t,e){t.addFieldOffset(2,e,0)}static startDictionariesVector(t,e){t.startVector(24,e,8)}static addRecordBatches(t,e){t.addFieldOffset(3,e,0)}static startRecordBatchesVector(t,e){t.startVector(24,e,8)}static addCustomMetadata(t,e){t.addFieldOffset(4,e,0)}static createCustomMetadataVector(t,e){t.startVector(4,e.length,4);for(let n=e.length-1;n>=0;n--)t.addOffset(e[n]);return t.endVector()}static startCustomMetadataVector(t,e){t.startVector(4,e,4)}static endFooter(t){return t.endObject()}static finishFooterBuffer(t,e){t.finish(e)}static finishSizePrefixedFooterBuffer(t,e){t.finish(e,void 0,!0)}}var Pr=Tr,kr=Lr,Vr=Fr;class zr{constructor(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:r.V4,n=arguments.length>2?arguments[2]:void 0,i=arguments.length>3?arguments[3]:void 0;this.schema=t,this.version=e,n&&(this._recordBatches=n),i&&(this._dictionaryBatches=i)}static decode(t){t=new Vr(J(t));const e=Rr.getRootAsFooter(t),n=Jn.decode(e.schema());return new $r(n,e)}static encode(t){const e=new kr,n=Jn.encode(e,t.schema);Rr.startRecordBatchesVector(e,t.numRecordBatches);for(const r of[...t.recordBatches()].slice().reverse())Wr.encode(e,r);const i=e.endVector();Rr.startDictionariesVector(e,t.numDictionaries);for(const r of[...t.dictionaryBatches()].slice().reverse())Wr.encode(e,r);const s=e.endVector();return Rr.startFooter(e),Rr.addSchema(e,n),Rr.addVersion(e,r.V4),Rr.addRecordBatches(e,i),Rr.addDictionaries(e,s),Rr.finishFooterBuffer(e,Rr.endFooter(e)),e.asUint8Array()}get numRecordBatches(){return this._recordBatches.length}get numDictionaries(){return this._dictionaryBatches.length}*recordBatches(){for(let t,e=-1,n=this.numRecordBatches;++e<n;)(t=this.getRecordBatch(e))&&(yield t)}*dictionaryBatches(){for(let t,e=-1,n=this.numDictionaries;++e<n;)(t=this.getDictionaryBatch(e))&&(yield t)}getRecordBatch(t){return t>=0&&t<this.numRecordBatches&&this._recordBatches[t]||null}getDictionaryBatch(t){return t>=0&&t<this.numDictionaries&&this._dictionaryBatches[t]||null}}class $r extends zr{constructor(t,e){super(t,e.version()),this._footer=e}get numRecordBatches(){return this._footer.recordBatchesLength()}get numDictionaries(){return this._footer.dictionariesLength()}getRecordBatch(t){if(t>=0&&t<this.numRecordBatches){const e=this._footer.recordBatches(t);if(e)return Wr.decode(e)}return null}getDictionaryBatch(t){if(t>=0&&t<this.numDictionaries){const e=this._footer.dictionaries(t);if(e)return Wr.decode(e)}return null}}class Wr{constructor(t,e,n){this.metaDataLength=t,this.offset="number"===typeof n?n:n.low,this.bodyLength="number"===typeof e?e:e.low}static decode(t){return new Wr(t.metaDataLength(),t.bodyLength(),t.offset())}static encode(t,e){const{metaDataLength:n}=e,r=new Pr(e.offset,0),i=new Pr(e.bodyLength,0);return vr.createBlock(t,r,n,i)}}const Yr={fromIterable:t=>Hr(function*(t){let e,n,r,i,s=!1,o=[],a=0;function l(){return"peek"===r?H(o,i)[0]:([n,o,a]=H(o,i),n)}({cmd:r,size:i}=yield null);const c=(u=t,Z(Uint8Array,u))[Symbol.iterator]();var u;try{do{if(({done:e,value:n}=Number.isNaN(i-a)?c.next():c.next(i-a)),!e&&n.byteLength>0&&(o.push(n),a+=n.byteLength),e||i<=a)do{({cmd:r,size:i}=yield l())}while(i<a)}while(!e)}catch(d){(s=!0)&&"function"===typeof c.throw&&c.throw(d)}finally{!1===s&&"function"===typeof c.return&&c.return(null)}return null}(t)),fromAsyncIterable:t=>Hr(function(t){return j(this,arguments,(function*(){let e,n,r,i,s=!1,o=[],a=0;function l(){return"peek"===r?H(o,i)[0]:([n,o,a]=H(o,i),n)}({cmd:r,size:i}=yield yield E(null));const c=(u=t,Q(Uint8Array,u))[Symbol.asyncIterator]();var u;try{do{if(({done:e,value:n}=Number.isNaN(i-a)?yield E(c.next()):yield E(c.next(i-a))),!e&&n.byteLength>0&&(o.push(n),a+=n.byteLength),e||i<=a)do{({cmd:r,size:i}=yield yield E(l()))}while(i<a)}while(!e)}catch(d){(s=!0)&&"function"===typeof c.throw&&(yield E(c.throw(d)))}finally{!1===s&&"function"===typeof c.return&&(yield E(c.return(new Uint8Array(0))))}return yield E(null)}))}(t)),fromDOMStream:t=>Hr(function(t){return j(this,arguments,(function*(){let e,n,r,i=!1,s=!1,o=[],a=0;function l(){return"peek"===n?H(o,r)[0]:([e,o,a]=H(o,r),e)}({cmd:n,size:r}=yield yield E(null));const c=new Kr(t);try{do{if(({done:i,value:e}=Number.isNaN(r-a)?yield E(c.read()):yield E(c.read(r-a))),!i&&e.byteLength>0&&(o.push(J(e)),a+=e.byteLength),i||r<=a)do{({cmd:n,size:r}=yield yield E(l()))}while(r<a)}while(!i)}catch(u){(s=!0)&&(yield E(c.cancel(u)))}finally{!1===s?yield E(c.cancel()):t.locked&&c.releaseLock()}return yield E(null)}))}(t)),fromNodeStream:t=>Hr(function(t){return j(this,arguments,(function*(){const e=[];let n,r,i,s="error",o=!1,a=null,l=0,c=[];function u(){return"peek"===n?H(c,r)[0]:([i,c,l]=H(c,r),i)}if(({cmd:n,size:r}=yield yield E(null)),t.isTTY)return yield yield E(new Uint8Array(0)),yield E(null);try{e[0]=Gr(t,"end"),e[1]=Gr(t,"error");do{if(e[2]=Gr(t,"readable"),[s,a]=yield E(Promise.race(e.map((t=>t[2])))),"error"===s)break;if((o="end"===s)||(Number.isFinite(r-l)?(i=J(t.read(r-l)),i.byteLength<r-l&&(i=J(t.read()))):i=J(t.read()),i.byteLength>0&&(c.push(i),l+=i.byteLength)),o||r<=l)do{({cmd:n,size:r}=yield yield E(u()))}while(r<l)}while(!o)}finally{yield E(d(e,"error"===s?a:null))}return yield E(null);function d(e,n){return i=c=null,new Promise(((r,i)=>{for(const[n,o]of e)t.off(n,o);try{const e=t.destroy;e&&e.call(t,n),n=void 0}catch(s){n=s||n}finally{null!=n?i(n):r()}}))}}))}(t)),toDOMStream(t,e){throw new Error('"toDOMStream" not available in this environment')},toNodeStream(t,e){throw new Error('"toNodeStream" not available in this environment')}},Hr=t=>(t.next(),t);class Kr{constructor(t){this.source=t,this.reader=null,this.reader=this.source.getReader(),this.reader.closed.catch((()=>{}))}get closed(){return this.reader?this.reader.closed.catch((()=>{})):Promise.resolve()}releaseLock(){this.reader&&this.reader.releaseLock(),this.reader=null}cancel(t){return U(this,void 0,void 0,(function*(){const{reader:e,source:n}=this;e&&(yield e.cancel(t).catch((()=>{}))),n&&n.locked&&this.releaseLock()}))}read(t){return U(this,void 0,void 0,(function*(){if(0===t)return{done:null==this.reader,value:new Uint8Array(0)};const e=yield this.reader.read();return!e.done&&(e.value=J(e)),e}))}}const Gr=(t,e)=>{const n=t=>r([e,t]);let r;return[e,n,new Promise((i=>(r=i)&&t.once(e,n)))]};const Jr=Object.freeze({done:!0,value:void 0});class qr{constructor(t){this._json=t}get schema(){return this._json.schema}get batches(){return this._json.batches||[]}get dictionaries(){return this._json.dictionaries||[]}}class Zr{tee(){return this._getDOMStream().tee()}pipe(t,e){return this._getNodeStream().pipe(t,e)}pipeTo(t,e){return this._getDOMStream().pipeTo(t,e)}pipeThrough(t,e){return this._getDOMStream().pipeThrough(t,e)}_getDOMStream(){return this._DOMStream||(this._DOMStream=this.toDOMStream())}_getNodeStream(){return this._nodeStream||(this._nodeStream=this.toNodeStream())}}class Qr extends Zr{constructor(){super(),this._values=[],this.resolvers=[],this._closedPromise=new Promise((t=>this._closedPromiseResolve=t))}get closed(){return this._closedPromise}cancel(t){return U(this,void 0,void 0,(function*(){yield this.return(t)}))}write(t){this._ensureOpen()&&(this.resolvers.length<=0?this._values.push(t):this.resolvers.shift().resolve({done:!1,value:t}))}abort(t){this._closedPromiseResolve&&(this.resolvers.length<=0?this._error={error:t}:this.resolvers.shift().reject({done:!0,value:t}))}close(){if(this._closedPromiseResolve){const{resolvers:t}=this;for(;t.length>0;)t.shift().resolve(Jr);this._closedPromiseResolve(),this._closedPromiseResolve=void 0}}[Symbol.asyncIterator](){return this}toDOMStream(t){return Yr.toDOMStream(this._closedPromiseResolve||this._error?this:this._values,t)}toNodeStream(t){return Yr.toNodeStream(this._closedPromiseResolve||this._error?this:this._values,t)}throw(t){return U(this,void 0,void 0,(function*(){return yield this.abort(t),Jr}))}return(t){return U(this,void 0,void 0,(function*(){return yield this.close(),Jr}))}read(t){return U(this,void 0,void 0,(function*(){return(yield this.next(t,"read")).value}))}peek(t){return U(this,void 0,void 0,(function*(){return(yield this.next(t,"peek")).value}))}next(){return this._values.length>0?Promise.resolve({done:!1,value:this._values.shift()}):this._error?Promise.reject({done:!0,value:this._error.error}):this._closedPromiseResolve?new Promise(((t,e)=>{this.resolvers.push({resolve:t,reject:e})})):Promise.resolve(Jr)}_ensureOpen(){if(this._closedPromiseResolve)return!0;throw new Error("AsyncQueue is closed")}}class Xr extends Qr{write(t){if((t=J(t)).byteLength>0)return super.write(t)}toString(){return arguments.length>0&&void 0!==arguments[0]&&arguments[0]?V(this.toUint8Array(!0)):this.toUint8Array(!1).then(V)}toUint8Array(){return arguments.length>0&&void 0!==arguments[0]&&arguments[0]?H(this._values)[0]:(()=>U(this,void 0,void 0,(function*(){var t,e;const n=[];let r=0;try{for(var i,s=P(this);!(i=yield s.next()).done;){const t=i.value;n.push(t),r+=t.byteLength}}catch(o){t={error:o}}finally{try{i&&!i.done&&(e=s.return)&&(yield e.call(s))}finally{if(t)throw t.error}}return H(n,r)[0]})))()}}class ti{constructor(t){t&&(this.source=new ni(Yr.fromIterable(t)))}[Symbol.iterator](){return this}next(t){return this.source.next(t)}throw(t){return this.source.throw(t)}return(t){return this.source.return(t)}peek(t){return this.source.peek(t)}read(t){return this.source.read(t)}}class ei{constructor(t){t instanceof ei?this.source=t.source:t instanceof Xr?this.source=new ri(Yr.fromAsyncIterable(t)):L(t)?this.source=new ri(Yr.fromNodeStream(t)):F(t)?this.source=new ri(Yr.fromDOMStream(t)):M(t)?this.source=new ri(Yr.fromDOMStream(t.body)):A(t)?this.source=new ri(Yr.fromIterable(t)):(S(t)||T(t))&&(this.source=new ri(Yr.fromAsyncIterable(t)))}[Symbol.asyncIterator](){return this}next(t){return this.source.next(t)}throw(t){return this.source.throw(t)}return(t){return this.source.return(t)}get closed(){return this.source.closed}cancel(t){return this.source.cancel(t)}peek(t){return this.source.peek(t)}read(t){return this.source.read(t)}}class ni{constructor(t){this.source=t}cancel(t){this.return(t)}peek(t){return this.next(t,"peek").value}read(t){return this.next(t,"read").value}next(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"read";return this.source.next({cmd:e,size:t})}throw(t){return Object.create(this.source.throw&&this.source.throw(t)||Jr)}return(t){return Object.create(this.source.return&&this.source.return(t)||Jr)}}class ri{constructor(t){this.source=t,this._closedPromise=new Promise((t=>this._closedPromiseResolve=t))}cancel(t){return U(this,void 0,void 0,(function*(){yield this.return(t)}))}get closed(){return this._closedPromise}read(t){return U(this,void 0,void 0,(function*(){return(yield this.next(t,"read")).value}))}peek(t){return U(this,void 0,void 0,(function*(){return(yield this.next(t,"peek")).value}))}next(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"read";return U(this,void 0,void 0,(function*(){return yield this.source.next({cmd:e,size:t})}))}throw(t){return U(this,void 0,void 0,(function*(){const e=this.source.throw&&(yield this.source.throw(t))||Jr;return this._closedPromiseResolve&&this._closedPromiseResolve(),this._closedPromiseResolve=void 0,Object.create(e)}))}return(t){return U(this,void 0,void 0,(function*(){const e=this.source.return&&(yield this.source.return(t))||Jr;return this._closedPromiseResolve&&this._closedPromiseResolve(),this._closedPromiseResolve=void 0,Object.create(e)}))}}class ii extends ti{constructor(t,e){super(),this.position=0,this.buffer=J(t),this.size="undefined"===typeof e?this.buffer.byteLength:e}readInt32(t){const{buffer:e,byteOffset:n}=this.readAt(t,4);return new DataView(e,n).getInt32(0,!0)}seek(t){return this.position=Math.min(t,this.size),t<this.size}read(t){const{buffer:e,size:n,position:r}=this;return e&&r<n?("number"!==typeof t&&(t=Number.POSITIVE_INFINITY),this.position=Math.min(n,r+Math.min(n-r,t)),e.subarray(r,this.position)):null}readAt(t,e){const n=this.buffer,r=Math.min(this.size,t+e);return n?n.subarray(t,r):new Uint8Array(e)}close(){this.buffer&&(this.buffer=null)}throw(t){return this.close(),{done:!0,value:t}}return(t){return this.close(),{done:!0,value:t}}}class si extends ei{constructor(t,e){super(),this.position=0,this._handle=t,"number"===typeof e?this.size=e:this._pending=(()=>U(this,void 0,void 0,(function*(){this.size=(yield t.stat()).size,delete this._pending})))()}readInt32(t){return U(this,void 0,void 0,(function*(){const{buffer:e,byteOffset:n}=yield this.readAt(t,4);return new DataView(e,n).getInt32(0,!0)}))}seek(t){return U(this,void 0,void 0,(function*(){return this._pending&&(yield this._pending),this.position=Math.min(t,this.size),t<this.size}))}read(t){return U(this,void 0,void 0,(function*(){this._pending&&(yield this._pending);const{_handle:e,size:n,position:r}=this;if(e&&r<n){"number"!==typeof t&&(t=Number.POSITIVE_INFINITY);let i=r,s=0,o=0;const a=Math.min(n,i+Math.min(n-i,t)),l=new Uint8Array(Math.max(0,(this.position=a)-i));for(;(i+=o)<a&&(s+=o)<l.byteLength;)({bytesRead:o}=yield e.read(l,s,l.byteLength-s,i));return l}return null}))}readAt(t,e){return U(this,void 0,void 0,(function*(){this._pending&&(yield this._pending);const{_handle:n,size:r}=this;if(n&&t+e<r){const i=Math.min(r,t+e),s=new Uint8Array(i-t);return(yield n.read(s,0,e,t)).buffer}return new Uint8Array(e)}))}close(){return U(this,void 0,void 0,(function*(){const t=this._handle;this._handle=null,t&&(yield t.close())}))}throw(t){return U(this,void 0,void 0,(function*(){return yield this.close(),{done:!0,value:t}}))}return(t){return U(this,void 0,void 0,(function*(){return yield this.close(),{done:!0,value:t}}))}}function oi(t){return t<0&&(t=4294967295+t+1),`0x${t.toString(16)}`}const ai=[1,10,100,1e3,1e4,1e5,1e6,1e7,1e8];class li{constructor(t){this.buffer=t}high(){return this.buffer[1]}low(){return this.buffer[0]}_times(t){const e=new Uint32Array([this.buffer[1]>>>16,65535&this.buffer[1],this.buffer[0]>>>16,65535&this.buffer[0]]),n=new Uint32Array([t.buffer[1]>>>16,65535&t.buffer[1],t.buffer[0]>>>16,65535&t.buffer[0]]);let r=e[3]*n[3];this.buffer[0]=65535&r;let i=r>>>16;return r=e[2]*n[3],i+=r,r=e[3]*n[2]>>>0,i+=r,this.buffer[0]+=i<<16,this.buffer[1]=i>>>0<r?65536:0,this.buffer[1]+=i>>>16,this.buffer[1]+=e[1]*n[3]+e[2]*n[2]+e[3]*n[1],this.buffer[1]+=e[0]*n[3]+e[1]*n[2]+e[2]*n[1]+e[3]*n[0]<<16,this}_plus(t){const e=this.buffer[0]+t.buffer[0]>>>0;this.buffer[1]+=t.buffer[1],e<this.buffer[0]>>>0&&++this.buffer[1],this.buffer[0]=e}lessThan(t){return this.buffer[1]<t.buffer[1]||this.buffer[1]===t.buffer[1]&&this.buffer[0]<t.buffer[0]}equals(t){return this.buffer[1]===t.buffer[1]&&this.buffer[0]==t.buffer[0]}greaterThan(t){return t.lessThan(this)}hex(){return`${oi(this.buffer[1])} ${oi(this.buffer[0])}`}}class ci extends li{times(t){return this._times(t),this}plus(t){return this._plus(t),this}static from(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new Uint32Array(2);return ci.fromString("string"===typeof t?t:t.toString(),e)}static fromNumber(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new Uint32Array(2);return ci.fromString(t.toString(),e)}static fromString(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new Uint32Array(2);const n=t.length,r=new ci(e);for(let i=0;i<n;){const e=8<n-i?8:n-i,s=new ci(new Uint32Array([Number.parseInt(t.slice(i,i+e),10),0])),o=new ci(new Uint32Array([ai[e],0]));r.times(o),r.plus(s),i+=e}return r}static convertArray(t){const e=new Uint32Array(2*t.length);for(let n=-1,r=t.length;++n<r;)ci.from(t[n],new Uint32Array(e.buffer,e.byteOffset+2*n*4,2));return e}static multiply(t,e){return new ci(new Uint32Array(t.buffer)).times(e)}static add(t,e){return new ci(new Uint32Array(t.buffer)).plus(e)}}class ui extends li{negate(){return this.buffer[0]=1+~this.buffer[0],this.buffer[1]=~this.buffer[1],0==this.buffer[0]&&++this.buffer[1],this}times(t){return this._times(t),this}plus(t){return this._plus(t),this}lessThan(t){const e=this.buffer[1]|0,n=t.buffer[1]|0;return e<n||e===n&&this.buffer[0]<t.buffer[0]}static from(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new Uint32Array(2);return ui.fromString("string"===typeof t?t:t.toString(),e)}static fromNumber(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new Uint32Array(2);return ui.fromString(t.toString(),e)}static fromString(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new Uint32Array(2);const n=t.startsWith("-"),r=t.length,i=new ui(e);for(let s=n?1:0;s<r;){const e=8<r-s?8:r-s,n=new ui(new Uint32Array([Number.parseInt(t.slice(s,s+e),10),0])),o=new ui(new Uint32Array([ai[e],0]));i.times(o),i.plus(n),s+=e}return n?i.negate():i}static convertArray(t){const e=new Uint32Array(2*t.length);for(let n=-1,r=t.length;++n<r;)ui.from(t[n],new Uint32Array(e.buffer,e.byteOffset+2*n*4,2));return e}static multiply(t,e){return new ui(new Uint32Array(t.buffer)).times(e)}static add(t,e){return new ui(new Uint32Array(t.buffer)).plus(e)}}class di{constructor(t){this.buffer=t}high(){return new ui(new Uint32Array(this.buffer.buffer,this.buffer.byteOffset+8,2))}low(){return new ui(new Uint32Array(this.buffer.buffer,this.buffer.byteOffset,2))}negate(){return this.buffer[0]=1+~this.buffer[0],this.buffer[1]=~this.buffer[1],this.buffer[2]=~this.buffer[2],this.buffer[3]=~this.buffer[3],0==this.buffer[0]&&++this.buffer[1],0==this.buffer[1]&&++this.buffer[2],0==this.buffer[2]&&++this.buffer[3],this}times(t){const e=new ci(new Uint32Array([this.buffer[3],0])),n=new ci(new Uint32Array([this.buffer[2],0])),r=new ci(new Uint32Array([this.buffer[1],0])),i=new ci(new Uint32Array([this.buffer[0],0])),s=new ci(new Uint32Array([t.buffer[3],0])),o=new ci(new Uint32Array([t.buffer[2],0])),a=new ci(new Uint32Array([t.buffer[1],0])),l=new ci(new Uint32Array([t.buffer[0],0]));let c=ci.multiply(i,l);this.buffer[0]=c.low();const u=new ci(new Uint32Array([c.high(),0]));c=ci.multiply(r,l),u.plus(c),c=ci.multiply(i,a),u.plus(c),this.buffer[1]=u.low(),this.buffer[3]=u.lessThan(c)?1:0,this.buffer[2]=u.high();return new ci(new Uint32Array(this.buffer.buffer,this.buffer.byteOffset+8,2)).plus(ci.multiply(n,l)).plus(ci.multiply(r,a)).plus(ci.multiply(i,o)),this.buffer[3]+=ci.multiply(e,l).plus(ci.multiply(n,a)).plus(ci.multiply(r,o)).plus(ci.multiply(i,s)).low(),this}plus(t){const e=new Uint32Array(4);return e[3]=this.buffer[3]+t.buffer[3]>>>0,e[2]=this.buffer[2]+t.buffer[2]>>>0,e[1]=this.buffer[1]+t.buffer[1]>>>0,e[0]=this.buffer[0]+t.buffer[0]>>>0,e[0]<this.buffer[0]>>>0&&++e[1],e[1]<this.buffer[1]>>>0&&++e[2],e[2]<this.buffer[2]>>>0&&++e[3],this.buffer[3]=e[3],this.buffer[2]=e[2],this.buffer[1]=e[1],this.buffer[0]=e[0],this}hex(){return`${oi(this.buffer[3])} ${oi(this.buffer[2])} ${oi(this.buffer[1])} ${oi(this.buffer[0])}`}static multiply(t,e){return new di(new Uint32Array(t.buffer)).times(e)}static add(t,e){return new di(new Uint32Array(t.buffer)).plus(e)}static from(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new Uint32Array(4);return di.fromString("string"===typeof t?t:t.toString(),e)}static fromNumber(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new Uint32Array(4);return di.fromString(t.toString(),e)}static fromString(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new Uint32Array(4);const n=t.startsWith("-"),r=t.length,i=new di(e);for(let s=n?1:0;s<r;){const e=8<r-s?8:r-s,n=new di(new Uint32Array([Number.parseInt(t.slice(s,s+e),10),0,0,0])),o=new di(new Uint32Array([ai[e],0,0,0]));i.times(o),i.plus(n),s+=e}return n?i.negate():i}static convertArray(t){const e=new Uint32Array(4*t.length);for(let n=-1,r=t.length;++n<r;)di.from(t[n],new Uint32Array(e.buffer,e.byteOffset+16*n,4));return e}}class hi extends Xt{constructor(t,e,n,r){super(),this.nodesIndex=-1,this.buffersIndex=-1,this.bytes=t,this.nodes=e,this.buffers=n,this.dictionaries=r}visit(t){return super.visit(t instanceof qn?t.type:t)}visitNull(t){let{length:e}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextFieldNode();return Gn({type:t,length:e})}visitBool(t){let{length:e,nullCount:n}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextFieldNode();return Gn({type:t,length:e,nullCount:n,nullBitmap:this.readNullBitmap(t,n),data:this.readData(t)})}visitInt(t){let{length:e,nullCount:n}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextFieldNode();return Gn({type:t,length:e,nullCount:n,nullBitmap:this.readNullBitmap(t,n),data:this.readData(t)})}visitFloat(t){let{length:e,nullCount:n}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextFieldNode();return Gn({type:t,length:e,nullCount:n,nullBitmap:this.readNullBitmap(t,n),data:this.readData(t)})}visitUtf8(t){let{length:e,nullCount:n}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextFieldNode();return Gn({type:t,length:e,nullCount:n,nullBitmap:this.readNullBitmap(t,n),valueOffsets:this.readOffsets(t),data:this.readData(t)})}visitBinary(t){let{length:e,nullCount:n}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextFieldNode();return Gn({type:t,length:e,nullCount:n,nullBitmap:this.readNullBitmap(t,n),valueOffsets:this.readOffsets(t),data:this.readData(t)})}visitFixedSizeBinary(t){let{length:e,nullCount:n}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextFieldNode();return Gn({type:t,length:e,nullCount:n,nullBitmap:this.readNullBitmap(t,n),data:this.readData(t)})}visitDate(t){let{length:e,nullCount:n}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextFieldNode();return Gn({type:t,length:e,nullCount:n,nullBitmap:this.readNullBitmap(t,n),data:this.readData(t)})}visitTimestamp(t){let{length:e,nullCount:n}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextFieldNode();return Gn({type:t,length:e,nullCount:n,nullBitmap:this.readNullBitmap(t,n),data:this.readData(t)})}visitTime(t){let{length:e,nullCount:n}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextFieldNode();return Gn({type:t,length:e,nullCount:n,nullBitmap:this.readNullBitmap(t,n),data:this.readData(t)})}visitDecimal(t){let{length:e,nullCount:n}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextFieldNode();return Gn({type:t,length:e,nullCount:n,nullBitmap:this.readNullBitmap(t,n),data:this.readData(t)})}visitList(t){let{length:e,nullCount:n}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextFieldNode();return Gn({type:t,length:e,nullCount:n,nullBitmap:this.readNullBitmap(t,n),valueOffsets:this.readOffsets(t),child:this.visit(t.children[0])})}visitStruct(t){let{length:e,nullCount:n}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextFieldNode();return Gn({type:t,length:e,nullCount:n,nullBitmap:this.readNullBitmap(t,n),children:this.visitMany(t.children)})}visitUnion(t){return t.mode===i.Sparse?this.visitSparseUnion(t):this.visitDenseUnion(t)}visitDenseUnion(t){let{length:e,nullCount:n}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextFieldNode();return Gn({type:t,length:e,nullCount:n,nullBitmap:this.readNullBitmap(t,n),typeIds:this.readTypeIds(t),valueOffsets:this.readOffsets(t),children:this.visitMany(t.children)})}visitSparseUnion(t){let{length:e,nullCount:n}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextFieldNode();return Gn({type:t,length:e,nullCount:n,nullBitmap:this.readNullBitmap(t,n),typeIds:this.readTypeIds(t),children:this.visitMany(t.children)})}visitDictionary(t){let{length:e,nullCount:n}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextFieldNode();return Gn({type:t,length:e,nullCount:n,nullBitmap:this.readNullBitmap(t,n),data:this.readData(t.indices),dictionary:this.readDictionary(t)})}visitInterval(t){let{length:e,nullCount:n}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextFieldNode();return Gn({type:t,length:e,nullCount:n,nullBitmap:this.readNullBitmap(t,n),data:this.readData(t)})}visitFixedSizeList(t){let{length:e,nullCount:n}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextFieldNode();return Gn({type:t,length:e,nullCount:n,nullBitmap:this.readNullBitmap(t,n),child:this.visit(t.children[0])})}visitMap(t){let{length:e,nullCount:n}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextFieldNode();return Gn({type:t,length:e,nullCount:n,nullBitmap:this.readNullBitmap(t,n),valueOffsets:this.readOffsets(t),child:this.visit(t.children[0])})}nextFieldNode(){return this.nodes[++this.nodesIndex]}nextBufferRange(){return this.buffers[++this.buffersIndex]}readNullBitmap(t,e){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this.nextBufferRange();return e>0&&this.readData(t,n)||new Uint8Array(0)}readOffsets(t,e){return this.readData(t,e)}readTypeIds(t,e){return this.readData(t,e)}readData(t){let{length:e,offset:n}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextBufferRange();return this.bytes.subarray(n,n+e)}readDictionary(t){return this.dictionaries.get(t.id)}}class fi extends hi{constructor(t,e,n,r){super(new Uint8Array(0),e,n,r),this.sources=t}readNullBitmap(t,e){let{offset:n}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this.nextBufferRange();return e<=0?new Uint8Array(0):An(this.sources[n])}readOffsets(t){let{offset:e}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextBufferRange();return K(Uint8Array,K(Int32Array,this.sources[e]))}readTypeIds(t){let{offset:e}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextBufferRange();return K(Uint8Array,K(t.ArrayType,this.sources[e]))}readData(t){let{offset:e}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextBufferRange();const{sources:n}=this;return xt.isTimestamp(t)||(xt.isInt(t)||xt.isTime(t))&&64===t.bitWidth||xt.isDate(t)&&t.unit===o.MILLISECOND?K(Uint8Array,ui.convertArray(n[e])):xt.isDecimal(t)?K(Uint8Array,di.convertArray(n[e])):xt.isBinary(t)||xt.isFixedSizeBinary(t)?function(t){const e=t.join(""),n=new Uint8Array(e.length/2);for(let r=0;r<e.length;r+=2)n[r>>1]=Number.parseInt(e.slice(r,r+2),16);return n}(n[e]):xt.isBool(t)?An(n[e]):xt.isUtf8(t)?$(n[e].join("")):K(Uint8Array,K(t.ArrayType,n[e].map((t=>+t))))}}var pi,yi,bi,_i,mi,gi,vi,wi;!function(t){t[t.BUFFER=0]="BUFFER"}(pi||(pi={})),function(t){t[t.LZ4_FRAME=0]="LZ4_FRAME",t[t.ZSTD=1]="ZSTD"}(yi||(yi={}));class Ii{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsBodyCompression(t,e){return(e||new Ii).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsBodyCompression(t,e){return t.setPosition(t.position()+4),(e||new Ii).__init(t.readInt32(t.position())+t.position(),t)}codec(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt8(this.bb_pos+t):yi.LZ4_FRAME}method(){const t=this.bb.__offset(this.bb_pos,6);return t?this.bb.readInt8(this.bb_pos+t):pi.BUFFER}static startBodyCompression(t){t.startObject(2)}static addCodec(t,e){t.addFieldInt8(0,e,yi.LZ4_FRAME)}static addMethod(t,e){t.addFieldInt8(1,e,pi.BUFFER)}static endBodyCompression(t){return t.endObject()}static createBodyCompression(t,e,n){return Ii.startBodyCompression(t),Ii.addCodec(t,e),Ii.addMethod(t,n),Ii.endBodyCompression(t)}}class Si{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}offset(){return this.bb.readInt64(this.bb_pos)}length(){return this.bb.readInt64(this.bb_pos+8)}static sizeOf(){return 16}static createBuffer(t,e,n){return t.prep(8,16),t.writeInt64(n),t.writeInt64(e),t.offset()}}class Ai{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}length(){return this.bb.readInt64(this.bb_pos)}nullCount(){return this.bb.readInt64(this.bb_pos+8)}static sizeOf(){return 16}static createFieldNode(t,e,n){return t.prep(8,16),t.writeInt64(n),t.writeInt64(e),t.offset()}}class Ti{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsRecordBatch(t,e){return(e||new Ti).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsRecordBatch(t,e){return t.setPosition(t.position()+4),(e||new Ti).__init(t.readInt32(t.position())+t.position(),t)}length(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt64(this.bb_pos+t):this.bb.createLong(0,0)}nodes(t,e){const n=this.bb.__offset(this.bb_pos,6);return n?(e||new Ai).__init(this.bb.__vector(this.bb_pos+n)+16*t,this.bb):null}nodesLength(){const t=this.bb.__offset(this.bb_pos,6);return t?this.bb.__vector_len(this.bb_pos+t):0}buffers(t,e){const n=this.bb.__offset(this.bb_pos,8);return n?(e||new Si).__init(this.bb.__vector(this.bb_pos+n)+16*t,this.bb):null}buffersLength(){const t=this.bb.__offset(this.bb_pos,8);return t?this.bb.__vector_len(this.bb_pos+t):0}compression(t){const e=this.bb.__offset(this.bb_pos,10);return e?(t||new Ii).__init(this.bb.__indirect(this.bb_pos+e),this.bb):null}static startRecordBatch(t){t.startObject(4)}static addLength(t,e){t.addFieldInt64(0,e,t.createLong(0,0))}static addNodes(t,e){t.addFieldOffset(1,e,0)}static startNodesVector(t,e){t.startVector(16,e,8)}static addBuffers(t,e){t.addFieldOffset(2,e,0)}static startBuffersVector(t,e){t.startVector(16,e,8)}static addCompression(t,e){t.addFieldOffset(3,e,0)}static endRecordBatch(t){return t.endObject()}}class Oi{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsDictionaryBatch(t,e){return(e||new Oi).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsDictionaryBatch(t,e){return t.setPosition(t.position()+4),(e||new Oi).__init(t.readInt32(t.position())+t.position(),t)}id(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt64(this.bb_pos+t):this.bb.createLong(0,0)}data(t){const e=this.bb.__offset(this.bb_pos,6);return e?(t||new Ti).__init(this.bb.__indirect(this.bb_pos+e),this.bb):null}isDelta(){const t=this.bb.__offset(this.bb_pos,8);return!!t&&!!this.bb.readInt8(this.bb_pos+t)}static startDictionaryBatch(t){t.startObject(3)}static addId(t,e){t.addFieldInt64(0,e,t.createLong(0,0))}static addData(t,e){t.addFieldOffset(1,e,0)}static addIsDelta(t,e){t.addFieldInt8(2,+e,0)}static endDictionaryBatch(t){return t.endObject()}}!function(t){t[t.HALF=0]="HALF",t[t.SINGLE=1]="SINGLE",t[t.DOUBLE=2]="DOUBLE"}(bi||(bi={}));class Bi{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsFloatingPoint(t,e){return(e||new Bi).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsFloatingPoint(t,e){return t.setPosition(t.position()+4),(e||new Bi).__init(t.readInt32(t.position())+t.position(),t)}precision(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):bi.HALF}static startFloatingPoint(t){t.startObject(1)}static addPrecision(t,e){t.addFieldInt16(0,e,bi.HALF)}static endFloatingPoint(t){return t.endObject()}static createFloatingPoint(t,e){return Bi.startFloatingPoint(t),Bi.addPrecision(t,e),Bi.endFloatingPoint(t)}}class Di{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsDecimal(t,e){return(e||new Di).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsDecimal(t,e){return t.setPosition(t.position()+4),(e||new Di).__init(t.readInt32(t.position())+t.position(),t)}precision(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt32(this.bb_pos+t):0}scale(){const t=this.bb.__offset(this.bb_pos,6);return t?this.bb.readInt32(this.bb_pos+t):0}bitWidth(){const t=this.bb.__offset(this.bb_pos,8);return t?this.bb.readInt32(this.bb_pos+t):128}static startDecimal(t){t.startObject(3)}static addPrecision(t,e){t.addFieldInt32(0,e,0)}static addScale(t,e){t.addFieldInt32(1,e,0)}static addBitWidth(t,e){t.addFieldInt32(2,e,128)}static endDecimal(t){return t.endObject()}static createDecimal(t,e,n,r){return Di.startDecimal(t),Di.addPrecision(t,e),Di.addScale(t,n),Di.addBitWidth(t,r),Di.endDecimal(t)}}!function(t){t[t.DAY=0]="DAY",t[t.MILLISECOND=1]="MILLISECOND"}(_i||(_i={}));class Mi{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsDate(t,e){return(e||new Mi).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsDate(t,e){return t.setPosition(t.position()+4),(e||new Mi).__init(t.readInt32(t.position())+t.position(),t)}unit(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):_i.MILLISECOND}static startDate(t){t.startObject(1)}static addUnit(t,e){t.addFieldInt16(0,e,_i.MILLISECOND)}static endDate(t){return t.endObject()}static createDate(t,e){return Mi.startDate(t),Mi.addUnit(t,e),Mi.endDate(t)}}!function(t){t[t.SECOND=0]="SECOND",t[t.MILLISECOND=1]="MILLISECOND",t[t.MICROSECOND=2]="MICROSECOND",t[t.NANOSECOND=3]="NANOSECOND"}(mi||(mi={}));class xi{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsTime(t,e){return(e||new xi).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsTime(t,e){return t.setPosition(t.position()+4),(e||new xi).__init(t.readInt32(t.position())+t.position(),t)}unit(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):mi.MILLISECOND}bitWidth(){const t=this.bb.__offset(this.bb_pos,6);return t?this.bb.readInt32(this.bb_pos+t):32}static startTime(t){t.startObject(2)}static addUnit(t,e){t.addFieldInt16(0,e,mi.MILLISECOND)}static addBitWidth(t,e){t.addFieldInt32(1,e,32)}static endTime(t){return t.endObject()}static createTime(t,e,n){return xi.startTime(t),xi.addUnit(t,e),xi.addBitWidth(t,n),xi.endTime(t)}}class Fi{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsTimestamp(t,e){return(e||new Fi).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsTimestamp(t,e){return t.setPosition(t.position()+4),(e||new Fi).__init(t.readInt32(t.position())+t.position(),t)}unit(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):mi.SECOND}timezone(t){const e=this.bb.__offset(this.bb_pos,6);return e?this.bb.__string(this.bb_pos+e,t):null}static startTimestamp(t){t.startObject(2)}static addUnit(t,e){t.addFieldInt16(0,e,mi.SECOND)}static addTimezone(t,e){t.addFieldOffset(1,e,0)}static endTimestamp(t){return t.endObject()}static createTimestamp(t,e,n){return Fi.startTimestamp(t),Fi.addUnit(t,e),Fi.addTimezone(t,n),Fi.endTimestamp(t)}}!function(t){t[t.YEAR_MONTH=0]="YEAR_MONTH",t[t.DAY_TIME=1]="DAY_TIME",t[t.MONTH_DAY_NANO=2]="MONTH_DAY_NANO"}(gi||(gi={}));class Li{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsInterval(t,e){return(e||new Li).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsInterval(t,e){return t.setPosition(t.position()+4),(e||new Li).__init(t.readInt32(t.position())+t.position(),t)}unit(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):gi.YEAR_MONTH}static startInterval(t){t.startObject(1)}static addUnit(t,e){t.addFieldInt16(0,e,gi.YEAR_MONTH)}static endInterval(t){return t.endObject()}static createInterval(t,e){return Li.startInterval(t),Li.addUnit(t,e),Li.endInterval(t)}}!function(t){t[t.Sparse=0]="Sparse",t[t.Dense=1]="Dense"}(vi||(vi={}));class Ni{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsUnion(t,e){return(e||new Ni).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsUnion(t,e){return t.setPosition(t.position()+4),(e||new Ni).__init(t.readInt32(t.position())+t.position(),t)}mode(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):vi.Sparse}typeIds(t){const e=this.bb.__offset(this.bb_pos,6);return e?this.bb.readInt32(this.bb.__vector(this.bb_pos+e)+4*t):0}typeIdsLength(){const t=this.bb.__offset(this.bb_pos,6);return t?this.bb.__vector_len(this.bb_pos+t):0}typeIdsArray(){const t=this.bb.__offset(this.bb_pos,6);return t?new Int32Array(this.bb.bytes().buffer,this.bb.bytes().byteOffset+this.bb.__vector(this.bb_pos+t),this.bb.__vector_len(this.bb_pos+t)):null}static startUnion(t){t.startObject(2)}static addMode(t,e){t.addFieldInt16(0,e,vi.Sparse)}static addTypeIds(t,e){t.addFieldOffset(1,e,0)}static createTypeIdsVector(t,e){t.startVector(4,e.length,4);for(let n=e.length-1;n>=0;n--)t.addInt32(e[n]);return t.endVector()}static startTypeIdsVector(t,e){t.startVector(4,e,4)}static endUnion(t){return t.endObject()}static createUnion(t,e,n){return Ni.startUnion(t),Ni.addMode(t,e),Ni.addTypeIds(t,n),Ni.endUnion(t)}}class Ui{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsFixedSizeBinary(t,e){return(e||new Ui).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsFixedSizeBinary(t,e){return t.setPosition(t.position()+4),(e||new Ui).__init(t.readInt32(t.position())+t.position(),t)}byteWidth(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt32(this.bb_pos+t):0}static startFixedSizeBinary(t){t.startObject(1)}static addByteWidth(t,e){t.addFieldInt32(0,e,0)}static endFixedSizeBinary(t){return t.endObject()}static createFixedSizeBinary(t,e){return Ui.startFixedSizeBinary(t),Ui.addByteWidth(t,e),Ui.endFixedSizeBinary(t)}}class Ci{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsFixedSizeList(t,e){return(e||new Ci).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsFixedSizeList(t,e){return t.setPosition(t.position()+4),(e||new Ci).__init(t.readInt32(t.position())+t.position(),t)}listSize(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt32(this.bb_pos+t):0}static startFixedSizeList(t){t.startObject(1)}static addListSize(t,e){t.addFieldInt32(0,e,0)}static endFixedSizeList(t){return t.endObject()}static createFixedSizeList(t,e){return Ci.startFixedSizeList(t),Ci.addListSize(t,e),Ci.endFixedSizeList(t)}}class Ei{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsMap(t,e){return(e||new Ei).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsMap(t,e){return t.setPosition(t.position()+4),(e||new Ei).__init(t.readInt32(t.position())+t.position(),t)}keysSorted(){const t=this.bb.__offset(this.bb_pos,4);return!!t&&!!this.bb.readInt8(this.bb_pos+t)}static startMap(t){t.startObject(1)}static addKeysSorted(t,e){t.addFieldInt8(0,+e,0)}static endMap(t){return t.endObject()}static createMap(t,e){return Ei.startMap(t),Ei.addKeysSorted(t,e),Ei.endMap(t)}}!function(t){t[t.NONE=0]="NONE",t[t.Schema=1]="Schema",t[t.DictionaryBatch=2]="DictionaryBatch",t[t.RecordBatch=3]="RecordBatch",t[t.Tensor=4]="Tensor",t[t.SparseTensor=5]="SparseTensor"}(wi||(wi={}));class ji{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsMessage(t,e){return(e||new ji).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsMessage(t,e){return t.setPosition(t.position()+4),(e||new ji).__init(t.readInt32(t.position())+t.position(),t)}version(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):Br.V1}headerType(){const t=this.bb.__offset(this.bb_pos,6);return t?this.bb.readUint8(this.bb_pos+t):wi.NONE}header(t){const e=this.bb.__offset(this.bb_pos,8);return e?this.bb.__union(t,this.bb_pos+e):null}bodyLength(){const t=this.bb.__offset(this.bb_pos,10);return t?this.bb.readInt64(this.bb_pos+t):this.bb.createLong(0,0)}customMetadata(t,e){const n=this.bb.__offset(this.bb_pos,12);return n?(e||new Nr).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+n)+4*t),this.bb):null}customMetadataLength(){const t=this.bb.__offset(this.bb_pos,12);return t?this.bb.__vector_len(this.bb_pos+t):0}static startMessage(t){t.startObject(5)}static addVersion(t,e){t.addFieldInt16(0,e,Br.V1)}static addHeaderType(t,e){t.addFieldInt8(1,e,wi.NONE)}static addHeader(t,e){t.addFieldOffset(2,e,0)}static addBodyLength(t,e){t.addFieldInt64(3,e,t.createLong(0,0))}static addCustomMetadata(t,e){t.addFieldOffset(4,e,0)}static createCustomMetadataVector(t,e){t.startVector(4,e.length,4);for(let n=e.length-1;n>=0;n--)t.addOffset(e[n]);return t.endVector()}static startCustomMetadataVector(t,e){t.startVector(4,e,4)}static endMessage(t){return t.endObject()}static finishMessageBuffer(t,e){t.finish(e)}static finishSizePrefixedMessageBuffer(t,e){t.finish(e,void 0,!0)}static createMessage(t,e,n,r,i,s){return ji.startMessage(t),ji.addVersion(t,e),ji.addHeaderType(t,n),ji.addHeader(t,r),ji.addBodyLength(t,i),ji.addCustomMetadata(t,s),ji.endMessage(t)}}class Ri{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsNull(t,e){return(e||new Ri).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsNull(t,e){return t.setPosition(t.position()+4),(e||new Ri).__init(t.readInt32(t.position())+t.position(),t)}static startNull(t){t.startObject(0)}static endNull(t){return t.endObject()}static createNull(t){return Ri.startNull(t),Ri.endNull(t)}}class Pi{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsBinary(t,e){return(e||new Pi).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsBinary(t,e){return t.setPosition(t.position()+4),(e||new Pi).__init(t.readInt32(t.position())+t.position(),t)}static startBinary(t){t.startObject(0)}static endBinary(t){return t.endObject()}static createBinary(t){return Pi.startBinary(t),Pi.endBinary(t)}}class ki{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsBool(t,e){return(e||new ki).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsBool(t,e){return t.setPosition(t.position()+4),(e||new ki).__init(t.readInt32(t.position())+t.position(),t)}static startBool(t){t.startObject(0)}static endBool(t){return t.endObject()}static createBool(t){return ki.startBool(t),ki.endBool(t)}}class Vi{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsUtf8(t,e){return(e||new Vi).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsUtf8(t,e){return t.setPosition(t.position()+4),(e||new Vi).__init(t.readInt32(t.position())+t.position(),t)}static startUtf8(t){t.startObject(0)}static endUtf8(t){return t.endObject()}static createUtf8(t){return Vi.startUtf8(t),Vi.endUtf8(t)}}class zi{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsList(t,e){return(e||new zi).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsList(t,e){return t.setPosition(t.position()+4),(e||new zi).__init(t.readInt32(t.position())+t.position(),t)}static startList(t){t.startObject(0)}static endList(t){return t.endObject()}static createList(t){return zi.startList(t),zi.endList(t)}}class $i{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsStruct_(t,e){return(e||new $i).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsStruct_(t,e){return t.setPosition(t.position()+4),(e||new $i).__init(t.readInt32(t.position())+t.position(),t)}static startStruct_(t){t.startObject(0)}static endStruct_(t){return t.endObject()}static createStruct_(t){return $i.startStruct_(t),$i.endStruct_(t)}}var Wi=Tr;const Yi=new class extends Xt{visit(t,e){return null==t||null==e?void 0:super.visit(t,e)}visitNull(t,e){return Ri.startNull(e),Ri.endNull(e)}visitInt(t,e){return Ur.startInt(e),Ur.addBitWidth(e,t.bitWidth),Ur.addIsSigned(e,t.isSigned),Ur.endInt(e)}visitFloat(t,e){return Bi.startFloatingPoint(e),Bi.addPrecision(e,t.precision),Bi.endFloatingPoint(e)}visitBinary(t,e){return Pi.startBinary(e),Pi.endBinary(e)}visitBool(t,e){return ki.startBool(e),ki.endBool(e)}visitUtf8(t,e){return Vi.startUtf8(e),Vi.endUtf8(e)}visitDecimal(t,e){return Di.startDecimal(e),Di.addScale(e,t.scale),Di.addPrecision(e,t.precision),Di.addBitWidth(e,t.bitWidth),Di.endDecimal(e)}visitDate(t,e){return Mi.startDate(e),Mi.addUnit(e,t.unit),Mi.endDate(e)}visitTime(t,e){return xi.startTime(e),xi.addUnit(e,t.unit),xi.addBitWidth(e,t.bitWidth),xi.endTime(e)}visitTimestamp(t,e){const n=t.timezone&&e.createString(t.timezone)||void 0;return Fi.startTimestamp(e),Fi.addUnit(e,t.unit),void 0!==n&&Fi.addTimezone(e,n),Fi.endTimestamp(e)}visitInterval(t,e){return Li.startInterval(e),Li.addUnit(e,t.unit),Li.endInterval(e)}visitList(t,e){return zi.startList(e),zi.endList(e)}visitStruct(t,e){return $i.startStruct_(e),$i.endStruct_(e)}visitUnion(t,e){Ni.startTypeIdsVector(e,t.typeIds.length);const n=Ni.createTypeIdsVector(e,t.typeIds);return Ni.startUnion(e),Ni.addMode(e,t.mode),Ni.addTypeIds(e,n),Ni.endUnion(e)}visitDictionary(t,e){const n=this.visit(t.indices,e);return Cr.startDictionaryEncoding(e),Cr.addId(e,new Wi(t.id,0)),Cr.addIsOrdered(e,t.isOrdered),void 0!==n&&Cr.addIndexType(e,n),Cr.endDictionaryEncoding(e)}visitFixedSizeBinary(t,e){return Ui.startFixedSizeBinary(e),Ui.addByteWidth(e,t.byteWidth),Ui.endFixedSizeBinary(e)}visitFixedSizeList(t,e){return Ci.startFixedSizeList(e),Ci.addListSize(e,t.listSize),Ci.endFixedSizeList(e)}visitMap(t,e){return Ei.startMap(e),Ei.addKeysSorted(e,t.keysSorted),Ei.endMap(e)}};function Hi(t){return new rs(t.count,Gi(t.columns),Ji(t.columns))}function Ki(t,e){return(t.children||[]).filter(Boolean).map((t=>qn.fromJSON(t,e)))}function Gi(t){return(t||[]).reduce(((t,e)=>{return[...t,new os(e.count,(n=e.VALIDITY,(n||[]).reduce(((t,e)=>t+ +(0===e)),0))),...Gi(e.children)];var n}),[])}function Ji(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];for(let n=-1,r=(t||[]).length;++n<r;){const r=t[n];r.VALIDITY&&e.push(new ss(e.length,r.VALIDITY.length)),r.TYPE&&e.push(new ss(e.length,r.TYPE.length)),r.OFFSET&&e.push(new ss(e.length,r.OFFSET.length)),r.DATA&&e.push(new ss(e.length,r.DATA.length)),e=Ji(r.children,e)}return e}function qi(t){return new Map(Object.entries(t||{}))}function Zi(t){return new Lt(t.isSigned,t.bitWidth)}function Qi(t,e){const n=t.type.name;switch(n){case"NONE":case"null":return new Ft;case"binary":return new Ct;case"utf8":return new Et;case"bool":return new jt;case"list":return new $t((e||[])[0]);case"struct":case"struct_":return new Wt(e||[])}switch(n){case"int":{const e=t.type;return new Lt(e.isSigned,e.bitWidth)}case"floatingpoint":{const e=t.type;return new Ut(s[e.precision])}case"decimal":{const e=t.type;return new Rt(e.scale,e.precision,e.bitWidth)}case"date":{const e=t.type;return new Pt(o[e.unit])}case"time":{const e=t.type;return new kt(a[e.unit],e.bitWidth)}case"timestamp":{const e=t.type;return new Vt(a[e.unit],e.timezone)}case"interval":{const e=t.type;return new zt(l[e.unit])}case"union":{const n=t.type;return new Yt(i[n.mode],n.typeIds||[],e||[])}case"fixedsizebinary":{const e=t.type;return new Ht(e.byteWidth)}case"fixedsizelist":{const n=t.type;return new Kt(n.listSize,(e||[])[0])}case"map":{const n=t.type;return new Gt((e||[])[0],n.keysSorted)}}throw new Error(`Unrecognized type: "${n}"`)}var Xi=Tr,ts=Lr,es=Fr;class ns{constructor(t,e,n,r){this._version=e,this._headerType=n,this.body=new Uint8Array(0),r&&(this._createHeader=()=>r),this._bodyLength="number"===typeof t?t:t.low}static fromJSON(t,e){const n=new ns(0,r.V4,e);return n._createHeader=function(t,e){return()=>{switch(e){case c.Schema:return Jn.fromJSON(t);case c.RecordBatch:return rs.fromJSON(t);case c.DictionaryBatch:return is.fromJSON(t)}throw new Error(`Unrecognized Message type: { name: ${c[e]}, type: ${e} }`)}}(t,e),n}static decode(t){t=new es(J(t));const e=ji.getRootAsMessage(t),n=e.bodyLength(),r=e.version(),i=e.headerType(),s=new ns(n,r,i);return s._createHeader=function(t,e){return()=>{switch(e){case c.Schema:return Jn.decode(t.header(new jr));case c.RecordBatch:return rs.decode(t.header(new Ti),t.version());case c.DictionaryBatch:return is.decode(t.header(new Oi),t.version())}throw new Error(`Unrecognized Message type: { name: ${c[e]}, type: ${e} }`)}}(e,i),s}static encode(t){const e=new ts;let n=-1;return t.isSchema()?n=Jn.encode(e,t.header()):t.isRecordBatch()?n=rs.encode(e,t.header()):t.isDictionaryBatch()&&(n=is.encode(e,t.header())),ji.startMessage(e),ji.addVersion(e,r.V4),ji.addHeader(e,n),ji.addHeaderType(e,t.headerType),ji.addBodyLength(e,new Xi(t.bodyLength,0)),ji.finishMessageBuffer(e,ji.endMessage(e)),e.asUint8Array()}static from(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;if(t instanceof Jn)return new ns(0,r.V4,c.Schema,t);if(t instanceof rs)return new ns(e,r.V4,c.RecordBatch,t);if(t instanceof is)return new ns(e,r.V4,c.DictionaryBatch,t);throw new Error(`Unrecognized Message header: ${t}`)}get type(){return this.headerType}get version(){return this._version}get headerType(){return this._headerType}get bodyLength(){return this._bodyLength}header(){return this._createHeader()}isSchema(){return this.headerType===c.Schema}isRecordBatch(){return this.headerType===c.RecordBatch}isDictionaryBatch(){return this.headerType===c.DictionaryBatch}}class rs{constructor(t,e,n){this._nodes=e,this._buffers=n,this._length="number"===typeof t?t:t.low}get nodes(){return this._nodes}get length(){return this._length}get buffers(){return this._buffers}}class is{constructor(t,e){let n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];this._data=t,this._isDelta=n,this._id="number"===typeof e?e:e.low}get id(){return this._id}get data(){return this._data}get isDelta(){return this._isDelta}get length(){return this.data.length}get nodes(){return this.data.nodes}get buffers(){return this.data.buffers}}class ss{constructor(t,e){this.offset="number"===typeof t?t:t.low,this.length="number"===typeof e?e:e.low}}class os{constructor(t,e){this.length="number"===typeof t?t:t.low,this.nullCount="number"===typeof e?e:e.low}}function as(t,e){const n=[];for(let r,i=-1,s=-1,o=t.childrenLength();++i<o;)(r=t.children(i))&&(n[++s]=qn.decode(r,e));return n}function ls(t){const e=new Map;if(t)for(let n,r,i=-1,s=Math.trunc(t.customMetadataLength());++i<s;)(n=t.customMetadata(i))&&null!=(r=n.key())&&e.set(r,n.value());return e}function cs(t){return new Lt(t.isSigned(),t.bitWidth())}function us(t,e){const n=t.typeType();switch(n){case xr.NONE:case xr.Null:return new Ft;case xr.Binary:return new Ct;case xr.Utf8:return new Et;case xr.Bool:return new jt;case xr.List:return new $t((e||[])[0]);case xr.Struct_:return new Wt(e||[])}switch(n){case xr.Int:{const e=t.type(new Ur);return new Lt(e.isSigned(),e.bitWidth())}case xr.FloatingPoint:{const e=t.type(new Bi);return new Ut(e.precision())}case xr.Decimal:{const e=t.type(new Di);return new Rt(e.scale(),e.precision(),e.bitWidth())}case xr.Date:{const e=t.type(new Mi);return new Pt(e.unit())}case xr.Time:{const e=t.type(new xi);return new kt(e.unit(),e.bitWidth())}case xr.Timestamp:{const e=t.type(new Fi);return new Vt(e.unit(),e.timezone())}case xr.Interval:{const e=t.type(new Li);return new zt(e.unit())}case xr.Union:{const n=t.type(new Ni);return new Yt(n.mode(),n.typeIdsArray()||[],e||[])}case xr.FixedSizeBinary:{const e=t.type(new Ui);return new Ht(e.byteWidth())}case xr.FixedSizeList:{const n=t.type(new Ci);return new Kt(n.listSize(),(e||[])[0])}case xr.Map:{const n=t.type(new Ei);return new Gt((e||[])[0],n.keysSorted())}}throw new Error(`Unrecognized type: "${xr[n]}" (${n})`)}qn.encode=function(t,e){let n=-1,r=-1,i=-1;const s=e.type;let o=e.typeId;xt.isDictionary(s)?(o=s.dictionary.typeId,i=Yi.visit(s,t),r=Yi.visit(s.dictionary,t)):r=Yi.visit(s,t);const a=(s.children||[]).map((e=>qn.encode(t,e))),l=Er.createChildrenVector(t,a),c=e.metadata&&e.metadata.size>0?Er.createCustomMetadataVector(t,[...e.metadata].map((e=>{let[n,r]=e;const i=t.createString(`${n}`),s=t.createString(`${r}`);return Nr.startKeyValue(t),Nr.addKey(t,i),Nr.addValue(t,s),Nr.endKeyValue(t)}))):-1;e.name&&(n=t.createString(e.name));Er.startField(t),Er.addType(t,r),Er.addTypeType(t,o),Er.addChildren(t,l),Er.addNullable(t,!!e.nullable),-1!==n&&Er.addName(t,n);-1!==i&&Er.addDictionary(t,i);-1!==c&&Er.addCustomMetadata(t,c);return Er.endField(t)},qn.decode=function(t,e){let n,r,i,s,o,a;e&&(a=t.dictionary())?e.has(n=a.id().low)?(s=(s=a.indexType())?cs(s):new Nt,o=new Zt(e.get(n),s,n,a.isOrdered()),r=new qn(t.name(),o,t.nullable(),ls(t))):(s=(s=a.indexType())?cs(s):new Nt,e.set(n,i=us(t,as(t,e))),o=new Zt(i,s,n,a.isOrdered()),r=new qn(t.name(),o,t.nullable(),ls(t))):(i=us(t,as(t,e)),r=new qn(t.name(),i,t.nullable(),ls(t)));return r||null},qn.fromJSON=function(t,e){let n,r,i,s,o,a;return e&&(s=t.dictionary)?e.has(n=s.id)?(r=(r=s.indexType)?Zi(r):new Nt,a=new Zt(e.get(n),r,n,s.isOrdered),i=new qn(t.name,a,t.nullable,qi(t.customMetadata))):(r=(r=s.indexType)?Zi(r):new Nt,e.set(n,o=Qi(t,Ki(t,e))),a=new Zt(o,r,n,s.isOrdered),i=new qn(t.name,a,t.nullable,qi(t.customMetadata))):(o=Qi(t,Ki(t,e)),i=new qn(t.name,o,t.nullable,qi(t.customMetadata))),i||null},Jn.encode=function(t,e){const n=e.fields.map((e=>qn.encode(t,e)));jr.startFieldsVector(t,n.length);const r=jr.createFieldsVector(t,n),i=e.metadata&&e.metadata.size>0?jr.createCustomMetadataVector(t,[...e.metadata].map((e=>{let[n,r]=e;const i=t.createString(`${n}`),s=t.createString(`${r}`);return Nr.startKeyValue(t),Nr.addKey(t,i),Nr.addValue(t,s),Nr.endKeyValue(t)}))):-1;jr.startSchema(t),jr.addFields(t,r),jr.addEndianness(t,ds?Dr.Little:Dr.Big),-1!==i&&jr.addCustomMetadata(t,i);return jr.endSchema(t)},Jn.decode=function(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new Map;const n=function(t,e){const n=[];for(let r,i=-1,s=-1,o=t.fieldsLength();++i<o;)(r=t.fields(i))&&(n[++s]=qn.decode(r,e));return n}(t,e);return new Jn(n,ls(t),e)},Jn.fromJSON=function(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new Map;return new Jn(function(t,e){return(t.fields||[]).filter(Boolean).map((t=>qn.fromJSON(t,e)))}(t,e),qi(t.customMetadata),e)},rs.encode=function(t,e){const n=e.nodes||[],r=e.buffers||[];Ti.startNodesVector(t,n.length);for(const o of n.slice().reverse())os.encode(t,o);const i=t.endVector();Ti.startBuffersVector(t,r.length);for(const o of r.slice().reverse())ss.encode(t,o);const s=t.endVector();return Ti.startRecordBatch(t),Ti.addLength(t,new Xi(e.length,0)),Ti.addNodes(t,i),Ti.addBuffers(t,s),Ti.endRecordBatch(t)},rs.decode=function(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:r.V4;if(null!==t.compression())throw new Error("Record batch compression not implemented");return new rs(t.length(),function(t){const e=[];for(let n,r=-1,i=-1,s=t.nodesLength();++r<s;)(n=t.nodes(r))&&(e[++i]=os.decode(n));return e}(t),function(t,e){const n=[];for(let i,s=-1,o=-1,a=t.buffersLength();++s<a;)(i=t.buffers(s))&&(e<r.V4&&(i.bb_pos+=8*(s+1)),n[++o]=ss.decode(i));return n}(t,e))},rs.fromJSON=Hi,is.encode=function(t,e){const n=rs.encode(t,e.data);return Oi.startDictionaryBatch(t),Oi.addId(t,new Xi(e.id,0)),Oi.addIsDelta(t,e.isDelta),Oi.addData(t,n),Oi.endDictionaryBatch(t)},is.decode=function(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:r.V4;return new is(rs.decode(t.data(),e),t.id(),t.isDelta())},is.fromJSON=function(t){return new is(Hi(t.data),t.id,t.isDelta)},os.encode=function(t,e){return Ai.createFieldNode(t,new Xi(e.length,0),new Xi(e.nullCount,0))},os.decode=function(t){return new os(t.length(),t.nullCount())},ss.encode=function(t,e){return Si.createBuffer(t,new Xi(e.offset,0),new Xi(e.length,0))},ss.decode=function(t){return new ss(t.offset(),t.length())};const ds=(()=>{const t=new ArrayBuffer(2);return new DataView(t).setInt16(0,256,!0),256===new Int16Array(t)[0]})(),hs=t=>`Expected ${c[t]} Message in stream, but was null or length 0.`,fs=t=>`Header pointer of flatbuffer-encoded ${c[t]} Message is null or length 0.`,ps=(t,e)=>`Expected to read ${t} metadata bytes, but only read ${e}.`,ys=(t,e)=>`Expected to read ${t} bytes for message body, but only read ${e}.`;class bs{constructor(t){this.source=t instanceof ti?t:new ti(t)}[Symbol.iterator](){return this}next(){let t;return(t=this.readMetadataLength()).done||-1===t.value&&(t=this.readMetadataLength()).done||(t=this.readMetadata(t.value)).done?Jr:t}throw(t){return this.source.throw(t)}return(t){return this.source.return(t)}readMessage(t){let e;if((e=this.next()).done)return null;if(null!=t&&e.value.headerType!==t)throw new Error(hs(t));return e.value}readMessageBody(t){if(t<=0)return new Uint8Array(0);const e=J(this.source.read(t));if(e.byteLength<t)throw new Error(ys(t,e.byteLength));return e.byteOffset%8===0&&e.byteOffset+e.byteLength<=e.buffer.byteLength?e:e.slice()}readSchema(){let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];const e=c.Schema,n=this.readMessage(e),r=null===n||void 0===n?void 0:n.header();if(t&&!r)throw new Error(fs(e));return r}readMetadataLength(){const t=this.source.read(gs),e=t&&new Fr(t),n=(null===e||void 0===e?void 0:e.readInt32(0))||0;return{done:0===n,value:n}}readMetadata(t){const e=this.source.read(t);if(!e)return Jr;if(e.byteLength<t)throw new Error(ps(t,e.byteLength));return{done:!1,value:ns.decode(e)}}}class _s{constructor(t,e){this.source=t instanceof ei?t:D(t)?new si(t,e):new ei(t)}[Symbol.asyncIterator](){return this}next(){return U(this,void 0,void 0,(function*(){let t;return(t=yield this.readMetadataLength()).done||-1===t.value&&(t=yield this.readMetadataLength()).done||(t=yield this.readMetadata(t.value)).done?Jr:t}))}throw(t){return U(this,void 0,void 0,(function*(){return yield this.source.throw(t)}))}return(t){return U(this,void 0,void 0,(function*(){return yield this.source.return(t)}))}readMessage(t){return U(this,void 0,void 0,(function*(){let e;if((e=yield this.next()).done)return null;if(null!=t&&e.value.headerType!==t)throw new Error(hs(t));return e.value}))}readMessageBody(t){return U(this,void 0,void 0,(function*(){if(t<=0)return new Uint8Array(0);const e=J(yield this.source.read(t));if(e.byteLength<t)throw new Error(ys(t,e.byteLength));return e.byteOffset%8===0&&e.byteOffset+e.byteLength<=e.buffer.byteLength?e:e.slice()}))}readSchema(){let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return U(this,void 0,void 0,(function*(){const e=c.Schema,n=yield this.readMessage(e),r=null===n||void 0===n?void 0:n.header();if(t&&!r)throw new Error(fs(e));return r}))}readMetadataLength(){return U(this,void 0,void 0,(function*(){const t=yield this.source.read(gs),e=t&&new Fr(t),n=(null===e||void 0===e?void 0:e.readInt32(0))||0;return{done:0===n,value:n}}))}readMetadata(t){return U(this,void 0,void 0,(function*(){const e=yield this.source.read(t);if(!e)return Jr;if(e.byteLength<t)throw new Error(ps(t,e.byteLength));return{done:!1,value:ns.decode(e)}}))}}class ms extends bs{constructor(t){super(new Uint8Array(0)),this._schema=!1,this._body=[],this._batchIndex=0,this._dictionaryIndex=0,this._json=t instanceof qr?t:new qr(t)}next(){const{_json:t}=this;if(!this._schema){this._schema=!0;return{done:!1,value:ns.fromJSON(t.schema,c.Schema)}}if(this._dictionaryIndex<t.dictionaries.length){const e=t.dictionaries[this._dictionaryIndex++];this._body=e.data.columns;return{done:!1,value:ns.fromJSON(e,c.DictionaryBatch)}}if(this._batchIndex<t.batches.length){const e=t.batches[this._batchIndex++];this._body=e.columns;return{done:!1,value:ns.fromJSON(e,c.RecordBatch)}}return this._body=[],Jr}readMessageBody(t){return function t(e){return(e||[]).reduce(((e,n)=>[...e,...n.VALIDITY&&[n.VALIDITY]||[],...n.TYPE&&[n.TYPE]||[],...n.OFFSET&&[n.OFFSET]||[],...n.DATA&&[n.DATA]||[],...t(n.children)]),[])}(this._body)}readMessage(t){let e;if((e=this.next()).done)return null;if(null!=t&&e.value.headerType!==t)throw new Error(hs(t));return e.value}readSchema(){const t=c.Schema,e=this.readMessage(t),n=null===e||void 0===e?void 0:e.header();if(!e||!n)throw new Error(fs(t));return n}}const gs=4,vs="ARROW1",ws=new Uint8Array(6);for(let so=0;so<6;so+=1)ws[so]=vs.codePointAt(so);function Is(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;for(let n=-1,r=ws.length;++n<r;)if(ws[n]!==t[e+n])return!1;return!0}const Ss=ws.length,As=Ss+gs,Ts=2*Ss+gs;class Os extends Zr{constructor(t){super(),this._impl=t}get closed(){return this._impl.closed}get schema(){return this._impl.schema}get autoDestroy(){return this._impl.autoDestroy}get dictionaries(){return this._impl.dictionaries}get numDictionaries(){return this._impl.numDictionaries}get numRecordBatches(){return this._impl.numRecordBatches}get footer(){return this._impl.isFile()?this._impl.footer:null}isSync(){return this._impl.isSync()}isAsync(){return this._impl.isAsync()}isFile(){return this._impl.isFile()}isStream(){return this._impl.isStream()}next(){return this._impl.next()}throw(t){return this._impl.throw(t)}return(t){return this._impl.return(t)}cancel(){return this._impl.cancel()}reset(t){return this._impl.reset(t),this._DOMStream=void 0,this._nodeStream=void 0,this}open(t){const e=this._impl.open(t);return S(e)?e.then((()=>this)):this}readRecordBatch(t){return this._impl.isFile()?this._impl.readRecordBatch(t):null}[Symbol.iterator](){return this._impl[Symbol.iterator]()}[Symbol.asyncIterator](){return this._impl[Symbol.asyncIterator]()}toDOMStream(){return Yr.toDOMStream(this.isSync()?{[Symbol.iterator]:()=>this}:{[Symbol.asyncIterator]:()=>this})}toNodeStream(){return Yr.toNodeStream(this.isSync()?{[Symbol.iterator]:()=>this}:{[Symbol.asyncIterator]:()=>this},{objectMode:!0})}static throughNode(t){throw new Error('"throughNode" not available in this environment')}static throughDOM(t,e){throw new Error('"throughDOM" not available in this environment')}static from(t){return t instanceof Os?t:O(t)?function(t){return new Bs(new Es(t))}(t):D(t)?function(t){return U(this,void 0,void 0,(function*(){const{size:e}=yield t.stat(),n=new si(t,e);return e>=Ts&&Is(yield n.readAt(0,Ss+7&-8))?new xs(new Cs(n)):new Ds(new Ns(n))}))}(t):S(t)?(()=>U(this,void 0,void 0,(function*(){return yield Os.from(yield t)})))():M(t)||F(t)||L(t)||T(t)?function(t){return U(this,void 0,void 0,(function*(){const e=yield t.peek(Ss+7&-8);return e&&e.byteLength>=4?Is(e)?new Ms(new Us(yield t.read())):new Ds(new Ns(t)):new Ds(new Ns(function(){return j(this,arguments,(function*(){}))}()))}))}(new ei(t)):function(t){const e=t.peek(Ss+7&-8);return e&&e.byteLength>=4?Is(e)?new Ms(new Us(t.read())):new Bs(new Ls(t)):new Bs(new Ls(function*(){}()))}(new ti(t))}static readAll(t){return t instanceof Os?t.isSync()?Rs(t):Ps(t):O(t)||ArrayBuffer.isView(t)||A(t)||B(t)?Rs(t):Ps(t)}}class Bs extends Os{constructor(t){super(t),this._impl=t}readAll(){return[...this]}[Symbol.iterator](){return this._impl[Symbol.iterator]()}[Symbol.asyncIterator](){return j(this,arguments,(function*(){yield E(yield*R(P(this[Symbol.iterator]())))}))}}class Ds extends Os{constructor(t){super(t),this._impl=t}readAll(){var t,e;return U(this,void 0,void 0,(function*(){const n=new Array;try{for(var r,i=P(this);!(r=yield i.next()).done;){const t=r.value;n.push(t)}}catch(s){t={error:s}}finally{try{r&&!r.done&&(e=i.return)&&(yield e.call(i))}finally{if(t)throw t.error}}return n}))}[Symbol.iterator](){throw new Error("AsyncRecordBatchStreamReader is not Iterable")}[Symbol.asyncIterator](){return this._impl[Symbol.asyncIterator]()}}class Ms extends Bs{constructor(t){super(t),this._impl=t}}class xs extends Ds{constructor(t){super(t),this._impl=t}}class Fs{constructor(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:new Map;this.closed=!1,this.autoDestroy=!0,this._dictionaryIndex=0,this._recordBatchIndex=0,this.dictionaries=t}get numDictionaries(){return this._dictionaryIndex}get numRecordBatches(){return this._recordBatchIndex}isSync(){return!1}isAsync(){return!1}isFile(){return!1}isStream(){return!1}reset(t){return this._dictionaryIndex=0,this._recordBatchIndex=0,this.schema=t,this.dictionaries=new Map,this}_loadRecordBatch(t,e){const n=this._loadVectors(t,e,this.schema.fields),r=Gn({type:new Wt(this.schema.fields),length:t.length,children:n});return new fr(this.schema,r)}_loadDictionaryBatch(t,e){const{id:n,isDelta:r}=t,{dictionaries:i,schema:s}=this,o=i.get(n);if(r||!o){const i=s.dictionaries.get(n),a=this._loadVectors(t.data,e,[i]);return(o&&r?o.concat(new Wn(a)):new Wn(a)).memoize()}return o.memoize()}_loadVectors(t,e,n){return new hi(e,t.nodes,t.buffers,this.dictionaries).visitMany(n)}}class Ls extends Fs{constructor(t,e){super(e),this._reader=O(t)?new ms(this._handle=t):new bs(this._handle=t)}isSync(){return!0}isStream(){return!0}[Symbol.iterator](){return this}cancel(){!this.closed&&(this.closed=!0)&&(this.reset()._reader.return(),this._reader=null,this.dictionaries=null)}open(t){return this.closed||(this.autoDestroy=js(this,t),this.schema||(this.schema=this._reader.readSchema())||this.cancel()),this}throw(t){return!this.closed&&this.autoDestroy&&(this.closed=!0)?this.reset()._reader.throw(t):Jr}return(t){return!this.closed&&this.autoDestroy&&(this.closed=!0)?this.reset()._reader.return(t):Jr}next(){if(this.closed)return Jr;let t;const{_reader:e}=this;for(;t=this._readNextMessageAndValidate();)if(t.isSchema())this.reset(t.header());else{if(t.isRecordBatch()){this._recordBatchIndex++;const n=t.header(),r=e.readMessageBody(t.bodyLength);return{done:!1,value:this._loadRecordBatch(n,r)}}if(t.isDictionaryBatch()){this._dictionaryIndex++;const n=t.header(),r=e.readMessageBody(t.bodyLength),i=this._loadDictionaryBatch(n,r);this.dictionaries.set(n.id,i)}}return this.schema&&0===this._recordBatchIndex?(this._recordBatchIndex++,{done:!1,value:new br(this.schema)}):this.return()}_readNextMessageAndValidate(t){return this._reader.readMessage(t)}}class Ns extends Fs{constructor(t,e){super(e),this._reader=new _s(this._handle=t)}isAsync(){return!0}isStream(){return!0}[Symbol.asyncIterator](){return this}cancel(){return U(this,void 0,void 0,(function*(){!this.closed&&(this.closed=!0)&&(yield this.reset()._reader.return(),this._reader=null,this.dictionaries=null)}))}open(t){return U(this,void 0,void 0,(function*(){return this.closed||(this.autoDestroy=js(this,t),this.schema||(this.schema=yield this._reader.readSchema())||(yield this.cancel())),this}))}throw(t){return U(this,void 0,void 0,(function*(){return!this.closed&&this.autoDestroy&&(this.closed=!0)?yield this.reset()._reader.throw(t):Jr}))}return(t){return U(this,void 0,void 0,(function*(){return!this.closed&&this.autoDestroy&&(this.closed=!0)?yield this.reset()._reader.return(t):Jr}))}next(){return U(this,void 0,void 0,(function*(){if(this.closed)return Jr;let t;const{_reader:e}=this;for(;t=yield this._readNextMessageAndValidate();)if(t.isSchema())yield this.reset(t.header());else{if(t.isRecordBatch()){this._recordBatchIndex++;const n=t.header(),r=yield e.readMessageBody(t.bodyLength);return{done:!1,value:this._loadRecordBatch(n,r)}}if(t.isDictionaryBatch()){this._dictionaryIndex++;const n=t.header(),r=yield e.readMessageBody(t.bodyLength),i=this._loadDictionaryBatch(n,r);this.dictionaries.set(n.id,i)}}return this.schema&&0===this._recordBatchIndex?(this._recordBatchIndex++,{done:!1,value:new br(this.schema)}):yield this.return()}))}_readNextMessageAndValidate(t){return U(this,void 0,void 0,(function*(){return yield this._reader.readMessage(t)}))}}class Us extends Ls{constructor(t,e){super(t instanceof ii?t:new ii(t),e)}get footer(){return this._footer}get numDictionaries(){return this._footer?this._footer.numDictionaries:0}get numRecordBatches(){return this._footer?this._footer.numRecordBatches:0}isSync(){return!0}isFile(){return!0}open(t){if(!this.closed&&!this._footer){this.schema=(this._footer=this._readFooter()).schema;for(const t of this._footer.dictionaryBatches())t&&this._readDictionaryBatch(this._dictionaryIndex++)}return super.open(t)}readRecordBatch(t){var e;if(this.closed)return null;this._footer||this.open();const n=null===(e=this._footer)||void 0===e?void 0:e.getRecordBatch(t);if(n&&this._handle.seek(n.offset)){const t=this._reader.readMessage(c.RecordBatch);if(null===t||void 0===t?void 0:t.isRecordBatch()){const e=t.header(),n=this._reader.readMessageBody(t.bodyLength);return this._loadRecordBatch(e,n)}}return null}_readDictionaryBatch(t){var e;const n=null===(e=this._footer)||void 0===e?void 0:e.getDictionaryBatch(t);if(n&&this._handle.seek(n.offset)){const t=this._reader.readMessage(c.DictionaryBatch);if(null===t||void 0===t?void 0:t.isDictionaryBatch()){const e=t.header(),n=this._reader.readMessageBody(t.bodyLength),r=this._loadDictionaryBatch(e,n);this.dictionaries.set(e.id,r)}}}_readFooter(){const{_handle:t}=this,e=t.size-As,n=t.readInt32(e),r=t.readAt(e-n,n);return zr.decode(r)}_readNextMessageAndValidate(t){var e;if(this._footer||this.open(),this._footer&&this._recordBatchIndex<this.numRecordBatches){const n=null===(e=this._footer)||void 0===e?void 0:e.getRecordBatch(this._recordBatchIndex);if(n&&this._handle.seek(n.offset))return this._reader.readMessage(t)}return null}}class Cs extends Ns{constructor(t){for(var e=arguments.length,n=new Array(e>1?e-1:0),r=1;r<e;r++)n[r-1]=arguments[r];const i="number"!==typeof n[0]?n.shift():void 0,s=n[0]instanceof Map?n.shift():void 0;super(t instanceof si?t:new si(t,i),s)}get footer(){return this._footer}get numDictionaries(){return this._footer?this._footer.numDictionaries:0}get numRecordBatches(){return this._footer?this._footer.numRecordBatches:0}isFile(){return!0}isAsync(){return!0}open(t){const e=Object.create(null,{open:{get:()=>super.open}});return U(this,void 0,void 0,(function*(){if(!this.closed&&!this._footer){this.schema=(this._footer=yield this._readFooter()).schema;for(const t of this._footer.dictionaryBatches())t&&(yield this._readDictionaryBatch(this._dictionaryIndex++))}return yield e.open.call(this,t)}))}readRecordBatch(t){var e;return U(this,void 0,void 0,(function*(){if(this.closed)return null;this._footer||(yield this.open());const n=null===(e=this._footer)||void 0===e?void 0:e.getRecordBatch(t);if(n&&(yield this._handle.seek(n.offset))){const t=yield this._reader.readMessage(c.RecordBatch);if(null===t||void 0===t?void 0:t.isRecordBatch()){const e=t.header(),n=yield this._reader.readMessageBody(t.bodyLength);return this._loadRecordBatch(e,n)}}return null}))}_readDictionaryBatch(t){var e;return U(this,void 0,void 0,(function*(){const n=null===(e=this._footer)||void 0===e?void 0:e.getDictionaryBatch(t);if(n&&(yield this._handle.seek(n.offset))){const t=yield this._reader.readMessage(c.DictionaryBatch);if(null===t||void 0===t?void 0:t.isDictionaryBatch()){const e=t.header(),n=yield this._reader.readMessageBody(t.bodyLength),r=this._loadDictionaryBatch(e,n);this.dictionaries.set(e.id,r)}}}))}_readFooter(){return U(this,void 0,void 0,(function*(){const{_handle:t}=this;t._pending&&(yield t._pending);const e=t.size-As,n=yield t.readInt32(e),r=yield t.readAt(e-n,n);return zr.decode(r)}))}_readNextMessageAndValidate(t){return U(this,void 0,void 0,(function*(){if(this._footer||(yield this.open()),this._footer&&this._recordBatchIndex<this.numRecordBatches){const e=this._footer.getRecordBatch(this._recordBatchIndex);if(e&&(yield this._handle.seek(e.offset)))return yield this._reader.readMessage(t)}return null}))}}class Es extends Ls{constructor(t,e){super(t,e)}_loadVectors(t,e,n){return new fi(e,t.nodes,t.buffers,this.dictionaries).visitMany(n)}}function js(t,e){return e&&"boolean"===typeof e.autoDestroy?e.autoDestroy:t.autoDestroy}function*Rs(t){const e=Os.from(t);try{if(!e.open({autoDestroy:!1}).closed)do{yield e}while(!e.reset().open().closed)}finally{e.cancel()}}function Ps(t){return j(this,arguments,(function*(){const e=yield E(Os.from(t));try{if(!(yield E(e.open({autoDestroy:!1}))).closed)do{yield yield E(e)}while(!(yield E(e.reset().open())).closed)}finally{yield E(e.cancel())}}))}class ks extends Xt{constructor(){super(),this._byteLength=0,this._nodes=[],this._buffers=[],this._bufferRegions=[]}static assemble(){const t=e=>e.flatMap((e=>Array.isArray(e)?t(e):e instanceof fr?e.data.children:e.data)),e=new ks;for(var n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return e.visitMany(t(r)),e}visit(t){if(t instanceof Wn)return this.visitMany(t.data),this;const{type:e}=t;if(!xt.isDictionary(e)){const{length:n,nullCount:r}=t;if(n>2147483647)throw new RangeError("Cannot write arrays larger than 2^31 - 1 in length");xt.isNull(e)||Vs.call(this,r<=0?new Uint8Array(0):Sn(t.offset,n,t.nullBitmap)),this.nodes.push(new os(n,r))}return super.visit(t)}visitNull(t){return this}visitDictionary(t){return this.visit(t.clone(t.type.indices))}get nodes(){return this._nodes}get buffers(){return this._buffers}get byteLength(){return this._byteLength}get bufferRegions(){return this._bufferRegions}}function Vs(t){const e=t.byteLength+7&-8;return this.buffers.push(t),this.bufferRegions.push(new ss(this._byteLength,e)),this._byteLength+=e,this}function zs(t){return Vs.call(this,t.values.subarray(0,t.length*t.stride))}function $s(t){const{length:e,values:n,valueOffsets:r}=t,i=r[0],s=r[e],o=Math.min(s-i,n.byteLength-i);return Vs.call(this,X(-r[0],e,r)),Vs.call(this,n.subarray(i,i+o)),this}function Ws(t){const{length:e,valueOffsets:n}=t;return n&&Vs.call(this,X(n[0],e,n)),this.visit(t.children[0])}function Ys(t){return this.visitMany(t.type.children.map(((e,n)=>t.children[n])).filter(Boolean))[0]}ks.prototype.visitBool=function(t){let e;return t.nullCount>=t.length?Vs.call(this,new Uint8Array(0)):(e=t.values)instanceof Uint8Array?Vs.call(this,Sn(t.offset,t.length,e)):Vs.call(this,An(t.values))},ks.prototype.visitInt=zs,ks.prototype.visitFloat=zs,ks.prototype.visitUtf8=$s,ks.prototype.visitBinary=$s,ks.prototype.visitFixedSizeBinary=zs,ks.prototype.visitDate=zs,ks.prototype.visitTimestamp=zs,ks.prototype.visitTime=zs,ks.prototype.visitDecimal=zs,ks.prototype.visitList=Ws,ks.prototype.visitStruct=Ys,ks.prototype.visitUnion=function(t){const{type:e,length:n,typeIds:r,valueOffsets:s}=t;if(Vs.call(this,r),e.mode===i.Sparse)return Ys.call(this,t);if(e.mode===i.Dense){if(t.offset<=0)return Vs.call(this,s),Ys.call(this,t);{const i=r.reduce(((t,e)=>Math.max(t,e)),r[0]),o=new Int32Array(i+1),a=new Int32Array(i+1).fill(-1),l=new Int32Array(n),c=X(-s[0],n,s);for(let t,e,s=-1;++s<n;)-1===(e=a[t=r[s]])&&(e=a[t]=c[t]),l[s]=c[s]-e,++o[t];Vs.call(this,l);for(let r,s=-1,u=e.children.length;++s<u;)if(r=t.children[s]){const t=e.typeIds[s],i=Math.min(n,o[t]);this.visit(r.slice(a[t],i))}}}return this},ks.prototype.visitInterval=zs,ks.prototype.visitFixedSizeList=Ws,ks.prototype.visitMap=Ws;class Hs extends Zr{constructor(t){super(),this._position=0,this._started=!1,this._sink=new Xr,this._schema=null,this._dictionaryBlocks=[],this._recordBatchBlocks=[],this._dictionaryDeltaOffsets=new Map,I(t)||(t={autoDestroy:!0,writeLegacyIpcFormat:!1}),this._autoDestroy="boolean"!==typeof t.autoDestroy||t.autoDestroy,this._writeLegacyIpcFormat="boolean"===typeof t.writeLegacyIpcFormat&&t.writeLegacyIpcFormat}static throughNode(t){throw new Error('"throughNode" not available in this environment')}static throughDOM(t,e){throw new Error('"throughDOM" not available in this environment')}toString(){let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return this._sink.toString(t)}toUint8Array(){let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return this._sink.toUint8Array(t)}writeAll(t){return S(t)?t.then((t=>this.writeAll(t))):T(t)?qs(this,t):Js(this,t)}get closed(){return this._sink.closed}[Symbol.asyncIterator](){return this._sink[Symbol.asyncIterator]()}toDOMStream(t){return this._sink.toDOMStream(t)}toNodeStream(t){return this._sink.toNodeStream(t)}close(){return this.reset()._sink.close()}abort(t){return this.reset()._sink.abort(t)}finish(){return this._autoDestroy?this.close():this.reset(this._sink,this._schema),this}reset(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this._sink,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;var n;return t===this._sink||t instanceof Xr?this._sink=t:(this._sink=new Xr,t&&(I(n=t)&&w(n.abort)&&w(n.getWriter)&&!x(n))?this.toDOMStream({type:"bytes"}).pipeTo(t):t&&(t=>I(t)&&w(t.end)&&w(t.write)&&v(t.writable)&&!x(t))(t)&&this.toNodeStream({objectMode:!1}).pipe(t)),this._started&&this._schema&&this._writeFooter(this._schema),this._started=!1,this._dictionaryBlocks=[],this._recordBatchBlocks=[],this._dictionaryDeltaOffsets=new Map,e&&ur(e,this._schema)||(null==e?(this._position=0,this._schema=null):(this._started=!0,this._schema=e,this._writeSchema(e))),this}write(t){let e=null;if(!this._sink)throw new Error("RecordBatchWriter is closed");if(null==t)return this.finish()&&void 0;if(t instanceof gr&&!(e=t.schema))return this.finish()&&void 0;if(t instanceof fr&&!(e=t.schema))return this.finish()&&void 0;if(e&&!ur(e,this._schema)){if(this._started&&this._autoDestroy)return this.close();this.reset(this._sink,e)}t instanceof fr?t instanceof br||this._writeRecordBatch(t):t instanceof gr?this.writeAll(t.batches):A(t)&&this.writeAll(t)}_writeMessage(t){const e=(arguments.length>1&&void 0!==arguments[1]?arguments[1]:8)-1,n=ns.encode(t),r=n.byteLength,i=this._writeLegacyIpcFormat?4:8,s=r+i+e&~e,o=s-r-i;return t.headerType===c.RecordBatch?this._recordBatchBlocks.push(new Wr(s,t.bodyLength,this._position)):t.headerType===c.DictionaryBatch&&this._dictionaryBlocks.push(new Wr(s,t.bodyLength,this._position)),this._writeLegacyIpcFormat||this._write(Int32Array.of(-1)),this._write(Int32Array.of(s-i)),r>0&&this._write(n),this._writePadding(o)}_write(t){if(this._started){const e=J(t);e&&e.byteLength>0&&(this._sink.write(e),this._position+=e.byteLength)}return this}_writeSchema(t){return this._writeMessage(ns.from(t))}_writeFooter(t){return this._writeLegacyIpcFormat?this._write(Int32Array.of(0)):this._write(Int32Array.of(-1,0))}_writeMagic(){return this._write(ws)}_writePadding(t){return t>0?this._write(new Uint8Array(t)):this}_writeRecordBatch(t){const{byteLength:e,nodes:n,bufferRegions:r,buffers:i}=ks.assemble(t),s=new rs(t.numRows,n,r),o=ns.from(s,e);return this._writeDictionaries(t)._writeMessage(o)._writeBodyBuffers(i)}_writeDictionaryBatch(t,e){let n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];this._dictionaryDeltaOffsets.set(e,t.length+(this._dictionaryDeltaOffsets.get(e)||0));const{byteLength:r,nodes:i,bufferRegions:s,buffers:o}=ks.assemble(new Wn([t])),a=new rs(t.length,i,s),l=new is(a,e,n),c=ns.from(l,r);return this._writeMessage(c)._writeBodyBuffers(o)}_writeBodyBuffers(t){let e,n,r;for(let i=-1,s=t.length;++i<s;)(e=t[i])&&(n=e.byteLength)>0&&(this._write(e),(r=(n+7&-8)-n)>0&&this._writePadding(r));return this}_writeDictionaries(t){for(let[e,n]of t.dictionaries){let t=this._dictionaryDeltaOffsets.get(e)||0;if(0===t||(n=null===n||void 0===n?void 0:n.slice(t)).length>0)for(const r of n.data)this._writeDictionaryBatch(r,e,t>0),t+=r.length}return this}}class Ks extends Hs{static writeAll(t,e){const n=new Ks(e);return S(t)?t.then((t=>n.writeAll(t))):T(t)?qs(n,t):Js(n,t)}}class Gs extends Hs{static writeAll(t){const e=new Gs;return S(t)?t.then((t=>e.writeAll(t))):T(t)?qs(e,t):Js(e,t)}constructor(){super(),this._autoDestroy=!0}_writeSchema(t){return this._writeMagic()._writePadding(2)}_writeFooter(t){const e=zr.encode(new zr(t,r.V4,this._recordBatchBlocks,this._dictionaryBlocks));return super._writeFooter(t)._write(e)._write(Int32Array.of(e.byteLength))._writeMagic()}}function Js(t,e){let n=e;e instanceof gr&&(n=e.batches,t.reset(void 0,e.schema));for(const r of n)t.write(r);return t.finish()}function qs(t,e){var n,r,i,s;return U(this,void 0,void 0,(function*(){try{for(n=P(e);!(r=yield n.next()).done;){const e=r.value;t.write(e)}}catch(o){i={error:o}}finally{try{r&&!r.done&&(s=n.return)&&(yield s.call(n))}finally{if(i)throw i.error}}return t.finish()}))}function Zs(t){const e=Os.from(t);return S(e)?e.then((t=>Zs(t))):e.isAsync()?e.readAll().then((t=>new gr(t))):new gr(e.readAll())}function Qs(t){return("stream"===(arguments.length>1&&void 0!==arguments[1]?arguments[1]:"stream")?Ks:Gs).writeAll(t).toUint8Array(!0)}var Xs,to=function(){function t(t,e,n,r){var i=this;this.getCell=function(t,e){var n=t<i.headerRows&&e<i.headerColumns,r=t>=i.headerRows&&e<i.headerColumns,s=t<i.headerRows&&e>=i.headerColumns;if(n){var o=["blank"];return e>0&&o.push("level"+t),{type:"blank",classNames:o.join(" "),content:""}}if(s)return{type:"columns",classNames:(o=["col_heading","level"+t,"col"+(l=e-i.headerColumns)]).join(" "),content:i.getContent(i.columnsTable,l,t)};if(r){o=["row_heading","level"+e,"row"+(a=t-i.headerRows)];return{type:"index",id:"T_".concat(i.uuid,"level").concat(e,"_row").concat(a),classNames:o.join(" "),content:i.getContent(i.indexTable,a,e)}}o=["data","row"+(a=t-i.headerRows),"col"+(l=e-i.headerColumns)];var a,l,c=i.styler?i.getContent(i.styler.displayValuesTable,a,l):i.getContent(i.dataTable,a,l);return{type:"data",id:"T_".concat(i.uuid,"row").concat(a,"_col").concat(l),classNames:o.join(" "),content:c}},this.getContent=function(t,e,n){var r=t.getChildAt(n);return null===r?"":i.getColumnTypeId(t,n)===u.Timestamp?i.nanosToDate(r.get(e)):r.get(e)},this.dataTable=Zs(t),this.indexTable=Zs(e),this.columnsTable=Zs(n),this.styler=r?{caption:r.caption,displayValuesTable:Zs(r.displayValues),styles:r.styles,uuid:r.uuid}:void 0}return Object.defineProperty(t.prototype,"rows",{get:function(){return this.indexTable.numRows+this.columnsTable.numCols},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"columns",{get:function(){return this.indexTable.numCols+this.columnsTable.numRows},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"headerRows",{get:function(){return this.rows-this.dataRows},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"headerColumns",{get:function(){return this.columns-this.dataColumns},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"dataRows",{get:function(){return this.dataTable.numRows},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"dataColumns",{get:function(){return this.dataTable.numCols},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"uuid",{get:function(){return this.styler&&this.styler.uuid},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"caption",{get:function(){return this.styler&&this.styler.caption},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"styles",{get:function(){return this.styler&&this.styler.styles},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"table",{get:function(){return this.dataTable},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"index",{get:function(){return this.indexTable},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"columnTable",{get:function(){return this.columnsTable},enumerable:!1,configurable:!0}),t.prototype.serialize=function(){return{data:Qs(this.dataTable),index:Qs(this.indexTable),columns:Qs(this.columnsTable)}},t.prototype.getColumnTypeId=function(t,e){return t.schema.fields[e].type.typeId},t.prototype.nanosToDate=function(t){return new Date(t/1e6)},t}(),eo=function(){return eo=Object.assign||function(t){for(var e,n=1,r=arguments.length;n<r;n++)for(var i in e=arguments[n])Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i]);return t},eo.apply(this,arguments)};!function(t){t.COMPONENT_READY="streamlit:componentReady",t.SET_COMPONENT_VALUE="streamlit:setComponentValue",t.SET_FRAME_HEIGHT="streamlit:setFrameHeight"}(Xs||(Xs={}));var no=function(){function t(){}return t.API_VERSION=1,t.RENDER_EVENT="streamlit:render",t.events=new EventTarget,t.registeredMessageListener=!1,t.setComponentReady=function(){t.registeredMessageListener||(window.addEventListener("message",t.onMessageEvent),t.registeredMessageListener=!0),t.sendBackMsg(Xs.COMPONENT_READY,{apiVersion:t.API_VERSION})},t.setFrameHeight=function(e){void 0===e&&(e=document.body.scrollHeight),e!==t.lastFrameHeight&&(t.lastFrameHeight=e,t.sendBackMsg(Xs.SET_FRAME_HEIGHT,{height:e}))},t.setComponentValue=function(e){var n;e instanceof to?(n="dataframe",e=e.serialize()):!function(t){var e=!1;try{e=t instanceof BigInt64Array||t instanceof BigUint64Array}catch(n){}return t instanceof Int8Array||t instanceof Uint8Array||t instanceof Uint8ClampedArray||t instanceof Int16Array||t instanceof Uint16Array||t instanceof Int32Array||t instanceof Uint32Array||t instanceof Float32Array||t instanceof Float64Array||e}(e)?e instanceof ArrayBuffer?(n="bytes",e=new Uint8Array(e)):n="json":(n="bytes",e=new Uint8Array(e.buffer)),t.sendBackMsg(Xs.SET_COMPONENT_VALUE,{value:e,dataType:n})},t.onMessageEvent=function(e){if(e.data.type===t.RENDER_EVENT)t.onRenderMessage(e.data)},t.onRenderMessage=function(e){var n=e.args;null==n&&(console.error("Got null args in onRenderMessage. This should never happen"),n={});var r=e.dfs&&e.dfs.length>0?t.argsDataframeToObject(e.dfs):{};n=eo(eo({},n),r);var i=Boolean(e.disabled),s=e.theme;s&&ro(s);var o={disabled:i,args:n,theme:s},a=new CustomEvent(t.RENDER_EVENT,{detail:o});t.events.dispatchEvent(a)},t.argsDataframeToObject=function(e){var n=e.map((function(e){var n=e.key,r=e.value;return[n,t.toArrowTable(r)]}));return Object.fromEntries(n)},t.toArrowTable=function(t){var e,n=(e=t.data).data,r=e.index,i=e.columns,s=e.styler;return new to(n,r,i,s)},t.sendBackMsg=function(t,e){window.parent.postMessage(eo({isStreamlitMessage:!0,type:t},e),"*")},t}(),ro=function(t){var e=document.createElement("style");document.head.appendChild(e),e.innerHTML="\n    :root {\n      --primary-color: ".concat(t.primaryColor,";\n      --background-color: ").concat(t.backgroundColor,";\n      --secondary-background-color: ").concat(t.secondaryBackgroundColor,";\n      --text-color: ").concat(t.textColor,";\n      --font: ").concat(t.font,";\n    }\n\n    body {\n      background-color: var(--background-color);\n      color: var(--text-color);\n    }\n  ")};var io=function(){var t=function(e,n){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])},t(e,n)};return function(e,n){if("function"!==typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function r(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();!function(t){function e(){return null!==t&&t.apply(this,arguments)||this}io(e,t),e.prototype.componentDidMount=function(){no.setFrameHeight()},e.prototype.componentDidUpdate=function(){no.setFrameHeight()}}(h.PureComponent)},453:(t,e,n)=>{n.d(e,{K:()=>a});const r=6378137;function i(t){return t*Math.PI/180}function s(t){return 180*t/Math.PI}function o(t,e,n,r){var o=i(t[1]),a=i(t[0]),l=e/n,c=Math.asin(Math.sin(o)*Math.cos(l)+Math.cos(o)*Math.sin(l)*Math.cos(r));return[s(a+Math.atan2(Math.sin(r)*Math.sin(l)*Math.cos(o),Math.cos(l)-Math.sin(o)*Math.sin(c))),s(c)]}function a(t,e){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:32;for(var i=[],s=0;s<n;++s)i.push(o(t,e,r,2*Math.PI*s/n));return i.push(i[0]),{type:"Polygon",coordinates:[i]}}},739:(t,e,n)=>{var r=n(123),i="function"===typeof Symbol&&Symbol.for,s=i?Symbol.for("react.element"):60103,o=i?Symbol.for("react.portal"):60106,a=i?Symbol.for("react.fragment"):60107,l=i?Symbol.for("react.strict_mode"):60108,c=i?Symbol.for("react.profiler"):60114,u=i?Symbol.for("react.provider"):60109,d=i?Symbol.for("react.context"):60110,h=i?Symbol.for("react.forward_ref"):60112,f=i?Symbol.for("react.suspense"):60113,p=i?Symbol.for("react.memo"):60115,y=i?Symbol.for("react.lazy"):60116,b="function"===typeof Symbol&&Symbol.iterator;function _(t){for(var e="https://reactjs.org/docs/error-decoder.html?invariant="+t,n=1;n<arguments.length;n++)e+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+t+"; visit "+e+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var m={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},g={};function v(t,e,n){this.props=t,this.context=e,this.refs=g,this.updater=n||m}function w(){}function I(t,e,n){this.props=t,this.context=e,this.refs=g,this.updater=n||m}v.prototype.isReactComponent={},v.prototype.setState=function(t,e){if("object"!==typeof t&&"function"!==typeof t&&null!=t)throw Error(_(85));this.updater.enqueueSetState(this,t,e,"setState")},v.prototype.forceUpdate=function(t){this.updater.enqueueForceUpdate(this,t,"forceUpdate")},w.prototype=v.prototype;var S=I.prototype=new w;S.constructor=I,r(S,v.prototype),S.isPureReactComponent=!0;var A={current:null},T=Object.prototype.hasOwnProperty,O={key:!0,ref:!0,__self:!0,__source:!0};function B(t,e,n){var r,i={},o=null,a=null;if(null!=e)for(r in void 0!==e.ref&&(a=e.ref),void 0!==e.key&&(o=""+e.key),e)T.call(e,r)&&!O.hasOwnProperty(r)&&(i[r]=e[r]);var l=arguments.length-2;if(1===l)i.children=n;else if(1<l){for(var c=Array(l),u=0;u<l;u++)c[u]=arguments[u+2];i.children=c}if(t&&t.defaultProps)for(r in l=t.defaultProps)void 0===i[r]&&(i[r]=l[r]);return{$$typeof:s,type:t,key:o,ref:a,props:i,_owner:A.current}}function D(t){return"object"===typeof t&&null!==t&&t.$$typeof===s}var M=/\/+/g,x=[];function F(t,e,n,r){if(x.length){var i=x.pop();return i.result=t,i.keyPrefix=e,i.func=n,i.context=r,i.count=0,i}return{result:t,keyPrefix:e,func:n,context:r,count:0}}function L(t){t.result=null,t.keyPrefix=null,t.func=null,t.context=null,t.count=0,10>x.length&&x.push(t)}function N(t,e,n,r){var i=typeof t;"undefined"!==i&&"boolean"!==i||(t=null);var a=!1;if(null===t)a=!0;else switch(i){case"string":case"number":a=!0;break;case"object":switch(t.$$typeof){case s:case o:a=!0}}if(a)return n(r,t,""===e?"."+C(t,0):e),1;if(a=0,e=""===e?".":e+":",Array.isArray(t))for(var l=0;l<t.length;l++){var c=e+C(i=t[l],l);a+=N(i,c,n,r)}else if(null===t||"object"!==typeof t?c=null:c="function"===typeof(c=b&&t[b]||t["@@iterator"])?c:null,"function"===typeof c)for(t=c.call(t),l=0;!(i=t.next()).done;)a+=N(i=i.value,c=e+C(i,l++),n,r);else if("object"===i)throw n=""+t,Error(_(31,"[object Object]"===n?"object with keys {"+Object.keys(t).join(", ")+"}":n,""));return a}function U(t,e,n){return null==t?0:N(t,"",e,n)}function C(t,e){return"object"===typeof t&&null!==t&&null!=t.key?function(t){var e={"=":"=0",":":"=2"};return"$"+(""+t).replace(/[=:]/g,(function(t){return e[t]}))}(t.key):e.toString(36)}function E(t,e){t.func.call(t.context,e,t.count++)}function j(t,e,n){var r=t.result,i=t.keyPrefix;t=t.func.call(t.context,e,t.count++),Array.isArray(t)?R(t,r,n,(function(t){return t})):null!=t&&(D(t)&&(t=function(t,e){return{$$typeof:s,type:t.type,key:e,ref:t.ref,props:t.props,_owner:t._owner}}(t,i+(!t.key||e&&e.key===t.key?"":(""+t.key).replace(M,"$&/")+"/")+n)),r.push(t))}function R(t,e,n,r,i){var s="";null!=n&&(s=(""+n).replace(M,"$&/")+"/"),U(t,j,e=F(e,s,r,i)),L(e)}var P={current:null};function k(){var t=P.current;if(null===t)throw Error(_(321));return t}var V={ReactCurrentDispatcher:P,ReactCurrentBatchConfig:{suspense:null},ReactCurrentOwner:A,IsSomeRendererActing:{current:!1},assign:r};e.PureComponent=I},787:(t,e,n)=>{n.d(e,{sg:()=>xe});var r={};n.r(r),n.d(r,{VERSION:()=>i,after:()=>Ue,all:()=>tn,allKeys:()=>bt,any:()=>en,assign:()=>Ut,before:()=>Ce,bind:()=>Ie,bindAll:()=>Te,chain:()=>me,chunk:()=>Rn,clone:()=>Rt,collect:()=>Ge,compact:()=>Bn,compose:()=>Ne,constant:()=>Q,contains:()=>nn,countBy:()=>_n,create:()=>jt,debounce:()=>xe,default:()=>Vn,defaults:()=>Ct,defer:()=>De,delay:()=>Be,detect:()=>Ye,difference:()=>Mn,drop:()=>Tn,each:()=>Ke,escape:()=>oe,every:()=>tn,extend:()=>Nt,extendOwn:()=>Ut,filter:()=>Qe,find:()=>Ye,findIndex:()=>Pe,findKey:()=>je,findLastIndex:()=>ke,findWhere:()=>He,first:()=>An,flatten:()=>Dn,foldl:()=>qe,foldr:()=>Ze,forEach:()=>Ke,functions:()=>Ft,get:()=>$t,groupBy:()=>yn,has:()=>Wt,head:()=>An,identity:()=>Yt,include:()=>nn,includes:()=>nn,indexBy:()=>bn,indexOf:()=>$e,initial:()=>Sn,inject:()=>qe,intersection:()=>Nn,invert:()=>xt,invoke:()=>rn,isArguments:()=>J,isArray:()=>H,isArrayBuffer:()=>j,isBoolean:()=>D,isDataView:()=>Y,isDate:()=>N,isElement:()=>M,isEmpty:()=>lt,isEqual:()=>yt,isError:()=>C,isFinite:()=>q,isFunction:()=>k,isMap:()=>At,isMatch:()=>ct,isNaN:()=>Z,isNull:()=>O,isNumber:()=>L,isObject:()=>T,isRegExp:()=>U,isSet:()=>Ot,isString:()=>F,isSymbol:()=>E,isTypedArray:()=>it,isUndefined:()=>B,isWeakMap:()=>Tt,isWeakSet:()=>Bt,iteratee:()=>qt,keys:()=>at,last:()=>On,lastIndexOf:()=>We,map:()=>Ge,mapObject:()=>Qt,matcher:()=>Ht,matches:()=>Ht,max:()=>an,memoize:()=>Oe,methods:()=>Ft,min:()=>ln,mixin:()=>kn,negate:()=>Le,noop:()=>Xt,now:()=>re,object:()=>En,omit:()=>In,once:()=>Ee,pairs:()=>Mt,partial:()=>we,partition:()=>mn,pick:()=>wn,pluck:()=>sn,property:()=>Kt,propertyOf:()=>te,random:()=>ne,range:()=>jn,reduce:()=>qe,reduceRight:()=>Ze,reject:()=>Xe,rest:()=>Tn,restArguments:()=>A,result:()=>ye,sample:()=>dn,select:()=>Qe,shuffle:()=>hn,size:()=>gn,some:()=>en,sortBy:()=>fn,sortedIndex:()=>Ve,tail:()=>Tn,take:()=>An,tap:()=>Pt,template:()=>pe,templateSettings:()=>le,throttle:()=>Me,times:()=>ee,toArray:()=>un,toPath:()=>kt,transpose:()=>Un,unescape:()=>ae,union:()=>Ln,uniq:()=>Fn,unique:()=>Fn,uniqueId:()=>_e,unzip:()=>Un,values:()=>Dt,where:()=>on,without:()=>xn,wrap:()=>Fe,zip:()=>Cn});var i="1.13.7",s="object"==typeof self&&self.self===self&&self||"object"==typeof global&&global.global===global&&global||Function("return this")()||{},o=Array.prototype,a=Object.prototype,l="undefined"!==typeof Symbol?Symbol.prototype:null,c=o.push,u=o.slice,d=a.toString,h=a.hasOwnProperty,f="undefined"!==typeof ArrayBuffer,p="undefined"!==typeof DataView,y=Array.isArray,b=Object.keys,_=Object.create,m=f&&ArrayBuffer.isView,g=isNaN,v=isFinite,w=!{toString:null}.propertyIsEnumerable("toString"),I=["valueOf","isPrototypeOf","toString","propertyIsEnumerable","hasOwnProperty","toLocaleString"],S=Math.pow(2,53)-1;function A(t,e){return e=null==e?t.length-1:+e,function(){for(var n=Math.max(arguments.length-e,0),r=Array(n),i=0;i<n;i++)r[i]=arguments[i+e];switch(e){case 0:return t.call(this,r);case 1:return t.call(this,arguments[0],r);case 2:return t.call(this,arguments[0],arguments[1],r)}var s=Array(e+1);for(i=0;i<e;i++)s[i]=arguments[i];return s[e]=r,t.apply(this,s)}}function T(t){var e=typeof t;return"function"===e||"object"===e&&!!t}function O(t){return null===t}function B(t){return void 0===t}function D(t){return!0===t||!1===t||"[object Boolean]"===d.call(t)}function M(t){return!(!t||1!==t.nodeType)}function x(t){var e="[object "+t+"]";return function(t){return d.call(t)===e}}const F=x("String"),L=x("Number"),N=x("Date"),U=x("RegExp"),C=x("Error"),E=x("Symbol"),j=x("ArrayBuffer");var R=x("Function"),P=s.document&&s.document.childNodes;"object"!=typeof Int8Array&&"function"!=typeof P&&(R=function(t){return"function"==typeof t||!1});const k=R,V=x("Object");var z=p&&(!/\[native code\]/.test(String(DataView))||V(new DataView(new ArrayBuffer(8)))),$="undefined"!==typeof Map&&V(new Map),W=x("DataView");const Y=z?function(t){return null!=t&&k(t.getInt8)&&j(t.buffer)}:W,H=y||x("Array");function K(t,e){return null!=t&&h.call(t,e)}var G=x("Arguments");!function(){G(arguments)||(G=function(t){return K(t,"callee")})}();const J=G;function q(t){return!E(t)&&v(t)&&!isNaN(parseFloat(t))}function Z(t){return L(t)&&g(t)}function Q(t){return function(){return t}}function X(t){return function(e){var n=t(e);return"number"==typeof n&&n>=0&&n<=S}}function tt(t){return function(e){return null==e?void 0:e[t]}}const et=tt("byteLength"),nt=X(et);var rt=/\[object ((I|Ui)nt(8|16|32)|Float(32|64)|Uint8Clamped|Big(I|Ui)nt64)Array\]/;const it=f?function(t){return m?m(t)&&!Y(t):nt(t)&&rt.test(d.call(t))}:Q(!1),st=tt("length");function ot(t,e){e=function(t){for(var e={},n=t.length,r=0;r<n;++r)e[t[r]]=!0;return{contains:function(t){return!0===e[t]},push:function(n){return e[n]=!0,t.push(n)}}}(e);var n=I.length,r=t.constructor,i=k(r)&&r.prototype||a,s="constructor";for(K(t,s)&&!e.contains(s)&&e.push(s);n--;)(s=I[n])in t&&t[s]!==i[s]&&!e.contains(s)&&e.push(s)}function at(t){if(!T(t))return[];if(b)return b(t);var e=[];for(var n in t)K(t,n)&&e.push(n);return w&&ot(t,e),e}function lt(t){if(null==t)return!0;var e=st(t);return"number"==typeof e&&(H(t)||F(t)||J(t))?0===e:0===st(at(t))}function ct(t,e){var n=at(e),r=n.length;if(null==t)return!r;for(var i=Object(t),s=0;s<r;s++){var o=n[s];if(e[o]!==i[o]||!(o in i))return!1}return!0}function ut(t){return t instanceof ut?t:this instanceof ut?void(this._wrapped=t):new ut(t)}function dt(t){return new Uint8Array(t.buffer||t,t.byteOffset||0,et(t))}ut.VERSION=i,ut.prototype.value=function(){return this._wrapped},ut.prototype.valueOf=ut.prototype.toJSON=ut.prototype.value,ut.prototype.toString=function(){return String(this._wrapped)};var ht="[object DataView]";function ft(t,e,n,r){if(t===e)return 0!==t||1/t===1/e;if(null==t||null==e)return!1;if(t!==t)return e!==e;var i=typeof t;return("function"===i||"object"===i||"object"==typeof e)&&pt(t,e,n,r)}function pt(t,e,n,r){t instanceof ut&&(t=t._wrapped),e instanceof ut&&(e=e._wrapped);var i=d.call(t);if(i!==d.call(e))return!1;if(z&&"[object Object]"==i&&Y(t)){if(!Y(e))return!1;i=ht}switch(i){case"[object RegExp]":case"[object String]":return""+t===""+e;case"[object Number]":return+t!==+t?+e!==+e:0===+t?1/+t===1/e:+t===+e;case"[object Date]":case"[object Boolean]":return+t===+e;case"[object Symbol]":return l.valueOf.call(t)===l.valueOf.call(e);case"[object ArrayBuffer]":case ht:return pt(dt(t),dt(e),n,r)}var s="[object Array]"===i;if(!s&&it(t)){if(et(t)!==et(e))return!1;if(t.buffer===e.buffer&&t.byteOffset===e.byteOffset)return!0;s=!0}if(!s){if("object"!=typeof t||"object"!=typeof e)return!1;var o=t.constructor,a=e.constructor;if(o!==a&&!(k(o)&&o instanceof o&&k(a)&&a instanceof a)&&"constructor"in t&&"constructor"in e)return!1}r=r||[];for(var c=(n=n||[]).length;c--;)if(n[c]===t)return r[c]===e;if(n.push(t),r.push(e),s){if((c=t.length)!==e.length)return!1;for(;c--;)if(!ft(t[c],e[c],n,r))return!1}else{var u,h=at(t);if(c=h.length,at(e).length!==c)return!1;for(;c--;)if(!K(e,u=h[c])||!ft(t[u],e[u],n,r))return!1}return n.pop(),r.pop(),!0}function yt(t,e){return ft(t,e)}function bt(t){if(!T(t))return[];var e=[];for(var n in t)e.push(n);return w&&ot(t,e),e}function _t(t){var e=st(t);return function(n){if(null==n)return!1;var r=bt(n);if(st(r))return!1;for(var i=0;i<e;i++)if(!k(n[t[i]]))return!1;return t!==It||!k(n[mt])}}var mt="forEach",gt=["clear","delete"],vt=["get","has","set"],wt=gt.concat(mt,vt),It=gt.concat(vt),St=["add"].concat(gt,mt,"has");const At=$?_t(wt):x("Map"),Tt=$?_t(It):x("WeakMap"),Ot=$?_t(St):x("Set"),Bt=x("WeakSet");function Dt(t){for(var e=at(t),n=e.length,r=Array(n),i=0;i<n;i++)r[i]=t[e[i]];return r}function Mt(t){for(var e=at(t),n=e.length,r=Array(n),i=0;i<n;i++)r[i]=[e[i],t[e[i]]];return r}function xt(t){for(var e={},n=at(t),r=0,i=n.length;r<i;r++)e[t[n[r]]]=n[r];return e}function Ft(t){var e=[];for(var n in t)k(t[n])&&e.push(n);return e.sort()}function Lt(t,e){return function(n){var r=arguments.length;if(e&&(n=Object(n)),r<2||null==n)return n;for(var i=1;i<r;i++)for(var s=arguments[i],o=t(s),a=o.length,l=0;l<a;l++){var c=o[l];e&&void 0!==n[c]||(n[c]=s[c])}return n}}const Nt=Lt(bt),Ut=Lt(at),Ct=Lt(bt,!0);function Et(t){if(!T(t))return{};if(_)return _(t);var e=function(){};e.prototype=t;var n=new e;return e.prototype=null,n}function jt(t,e){var n=Et(t);return e&&Ut(n,e),n}function Rt(t){return T(t)?H(t)?t.slice():Nt({},t):t}function Pt(t,e){return e(t),t}function kt(t){return H(t)?t:[t]}function Vt(t){return ut.toPath(t)}function zt(t,e){for(var n=e.length,r=0;r<n;r++){if(null==t)return;t=t[e[r]]}return n?t:void 0}function $t(t,e,n){var r=zt(t,Vt(e));return B(r)?n:r}function Wt(t,e){for(var n=(e=Vt(e)).length,r=0;r<n;r++){var i=e[r];if(!K(t,i))return!1;t=t[i]}return!!n}function Yt(t){return t}function Ht(t){return t=Ut({},t),function(e){return ct(e,t)}}function Kt(t){return t=Vt(t),function(e){return zt(e,t)}}function Gt(t,e,n){if(void 0===e)return t;switch(null==n?3:n){case 1:return function(n){return t.call(e,n)};case 3:return function(n,r,i){return t.call(e,n,r,i)};case 4:return function(n,r,i,s){return t.call(e,n,r,i,s)}}return function(){return t.apply(e,arguments)}}function Jt(t,e,n){return null==t?Yt:k(t)?Gt(t,e,n):T(t)&&!H(t)?Ht(t):Kt(t)}function qt(t,e){return Jt(t,e,1/0)}function Zt(t,e,n){return ut.iteratee!==qt?ut.iteratee(t,e):Jt(t,e,n)}function Qt(t,e,n){e=Zt(e,n);for(var r=at(t),i=r.length,s={},o=0;o<i;o++){var a=r[o];s[a]=e(t[a],a,t)}return s}function Xt(){}function te(t){return null==t?Xt:function(e){return $t(t,e)}}function ee(t,e,n){var r=Array(Math.max(0,t));e=Gt(e,n,1);for(var i=0;i<t;i++)r[i]=e(i);return r}function ne(t,e){return null==e&&(e=t,t=0),t+Math.floor(Math.random()*(e-t+1))}ut.toPath=kt,ut.iteratee=qt;const re=Date.now||function(){return(new Date).getTime()};function ie(t){var e=function(e){return t[e]},n="(?:"+at(t).join("|")+")",r=RegExp(n),i=RegExp(n,"g");return function(t){return t=null==t?"":""+t,r.test(t)?t.replace(i,e):t}}const se={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#x27;","`":"&#x60;"},oe=ie(se),ae=ie(xt(se)),le=ut.templateSettings={evaluate:/<%([\s\S]+?)%>/g,interpolate:/<%=([\s\S]+?)%>/g,escape:/<%-([\s\S]+?)%>/g};var ce=/(.)^/,ue={"'":"'","\\":"\\","\r":"r","\n":"n","\u2028":"u2028","\u2029":"u2029"},de=/\\|'|\r|\n|\u2028|\u2029/g;function he(t){return"\\"+ue[t]}var fe=/^\s*(\w|\$)+\s*$/;function pe(t,e,n){!e&&n&&(e=n),e=Ct({},e,ut.templateSettings);var r=RegExp([(e.escape||ce).source,(e.interpolate||ce).source,(e.evaluate||ce).source].join("|")+"|$","g"),i=0,s="__p+='";t.replace(r,(function(e,n,r,o,a){return s+=t.slice(i,a).replace(de,he),i=a+e.length,n?s+="'+\n((__t=("+n+"))==null?'':_.escape(__t))+\n'":r?s+="'+\n((__t=("+r+"))==null?'':__t)+\n'":o&&(s+="';\n"+o+"\n__p+='"),e})),s+="';\n";var o,a=e.variable;if(a){if(!fe.test(a))throw new Error("variable is not a bare identifier: "+a)}else s="with(obj||{}){\n"+s+"}\n",a="obj";s="var __t,__p='',__j=Array.prototype.join,print=function(){__p+=__j.call(arguments,'');};\n"+s+"return __p;\n";try{o=new Function(a,"_",s)}catch(c){throw c.source=s,c}var l=function(t){return o.call(this,t,ut)};return l.source="function("+a+"){\n"+s+"}",l}function ye(t,e,n){var r=(e=Vt(e)).length;if(!r)return k(n)?n.call(t):n;for(var i=0;i<r;i++){var s=null==t?void 0:t[e[i]];void 0===s&&(s=n,i=r),t=k(s)?s.call(t):s}return t}var be=0;function _e(t){var e=++be+"";return t?t+e:e}function me(t){var e=ut(t);return e._chain=!0,e}function ge(t,e,n,r,i){if(!(r instanceof e))return t.apply(n,i);var s=Et(t.prototype),o=t.apply(s,i);return T(o)?o:s}var ve=A((function(t,e){var n=ve.placeholder,r=function(){for(var i=0,s=e.length,o=Array(s),a=0;a<s;a++)o[a]=e[a]===n?arguments[i++]:e[a];for(;i<arguments.length;)o.push(arguments[i++]);return ge(t,r,this,this,o)};return r}));ve.placeholder=ut;const we=ve,Ie=A((function(t,e,n){if(!k(t))throw new TypeError("Bind must be called on a function");var r=A((function(i){return ge(t,r,e,this,n.concat(i))}));return r})),Se=X(st);function Ae(t,e,n,r){if(r=r||[],e||0===e){if(e<=0)return r.concat(t)}else e=1/0;for(var i=r.length,s=0,o=st(t);s<o;s++){var a=t[s];if(Se(a)&&(H(a)||J(a)))if(e>1)Ae(a,e-1,n,r),i=r.length;else for(var l=0,c=a.length;l<c;)r[i++]=a[l++];else n||(r[i++]=a)}return r}const Te=A((function(t,e){var n=(e=Ae(e,!1,!1)).length;if(n<1)throw new Error("bindAll must be passed function names");for(;n--;){var r=e[n];t[r]=Ie(t[r],t)}return t}));function Oe(t,e){var n=function(r){var i=n.cache,s=""+(e?e.apply(this,arguments):r);return K(i,s)||(i[s]=t.apply(this,arguments)),i[s]};return n.cache={},n}const Be=A((function(t,e,n){return setTimeout((function(){return t.apply(null,n)}),e)})),De=we(Be,ut,1);function Me(t,e,n){var r,i,s,o,a=0;n||(n={});var l=function(){a=!1===n.leading?0:re(),r=null,o=t.apply(i,s),r||(i=s=null)},c=function(){var c=re();a||!1!==n.leading||(a=c);var u=e-(c-a);return i=this,s=arguments,u<=0||u>e?(r&&(clearTimeout(r),r=null),a=c,o=t.apply(i,s),r||(i=s=null)):r||!1===n.trailing||(r=setTimeout(l,u)),o};return c.cancel=function(){clearTimeout(r),a=0,r=i=s=null},c}function xe(t,e,n){var r,i,s,o,a,l=function(){var c=re()-i;e>c?r=setTimeout(l,e-c):(r=null,n||(o=t.apply(a,s)),r||(s=a=null))},c=A((function(c){return a=this,s=c,i=re(),r||(r=setTimeout(l,e),n&&(o=t.apply(a,s))),o}));return c.cancel=function(){clearTimeout(r),r=s=a=null},c}function Fe(t,e){return we(e,t)}function Le(t){return function(){return!t.apply(this,arguments)}}function Ne(){var t=arguments,e=t.length-1;return function(){for(var n=e,r=t[e].apply(this,arguments);n--;)r=t[n].call(this,r);return r}}function Ue(t,e){return function(){if(--t<1)return e.apply(this,arguments)}}function Ce(t,e){var n;return function(){return--t>0&&(n=e.apply(this,arguments)),t<=1&&(e=null),n}}const Ee=we(Ce,2);function je(t,e,n){e=Zt(e,n);for(var r,i=at(t),s=0,o=i.length;s<o;s++)if(e(t[r=i[s]],r,t))return r}function Re(t){return function(e,n,r){n=Zt(n,r);for(var i=st(e),s=t>0?0:i-1;s>=0&&s<i;s+=t)if(n(e[s],s,e))return s;return-1}}const Pe=Re(1),ke=Re(-1);function Ve(t,e,n,r){for(var i=(n=Zt(n,r,1))(e),s=0,o=st(t);s<o;){var a=Math.floor((s+o)/2);n(t[a])<i?s=a+1:o=a}return s}function ze(t,e,n){return function(r,i,s){var o=0,a=st(r);if("number"==typeof s)t>0?o=s>=0?s:Math.max(s+a,o):a=s>=0?Math.min(s+1,a):s+a+1;else if(n&&s&&a)return r[s=n(r,i)]===i?s:-1;if(i!==i)return(s=e(u.call(r,o,a),Z))>=0?s+o:-1;for(s=t>0?o:a-1;s>=0&&s<a;s+=t)if(r[s]===i)return s;return-1}}const $e=ze(1,Pe,Ve),We=ze(-1,ke);function Ye(t,e,n){var r=(Se(t)?Pe:je)(t,e,n);if(void 0!==r&&-1!==r)return t[r]}function He(t,e){return Ye(t,Ht(e))}function Ke(t,e,n){var r,i;if(e=Gt(e,n),Se(t))for(r=0,i=t.length;r<i;r++)e(t[r],r,t);else{var s=at(t);for(r=0,i=s.length;r<i;r++)e(t[s[r]],s[r],t)}return t}function Ge(t,e,n){e=Zt(e,n);for(var r=!Se(t)&&at(t),i=(r||t).length,s=Array(i),o=0;o<i;o++){var a=r?r[o]:o;s[o]=e(t[a],a,t)}return s}function Je(t){return function(e,n,r,i){var s=arguments.length>=3;return function(e,n,r,i){var s=!Se(e)&&at(e),o=(s||e).length,a=t>0?0:o-1;for(i||(r=e[s?s[a]:a],a+=t);a>=0&&a<o;a+=t){var l=s?s[a]:a;r=n(r,e[l],l,e)}return r}(e,Gt(n,i,4),r,s)}}const qe=Je(1),Ze=Je(-1);function Qe(t,e,n){var r=[];return e=Zt(e,n),Ke(t,(function(t,n,i){e(t,n,i)&&r.push(t)})),r}function Xe(t,e,n){return Qe(t,Le(Zt(e)),n)}function tn(t,e,n){e=Zt(e,n);for(var r=!Se(t)&&at(t),i=(r||t).length,s=0;s<i;s++){var o=r?r[s]:s;if(!e(t[o],o,t))return!1}return!0}function en(t,e,n){e=Zt(e,n);for(var r=!Se(t)&&at(t),i=(r||t).length,s=0;s<i;s++){var o=r?r[s]:s;if(e(t[o],o,t))return!0}return!1}function nn(t,e,n,r){return Se(t)||(t=Dt(t)),("number"!=typeof n||r)&&(n=0),$e(t,e,n)>=0}const rn=A((function(t,e,n){var r,i;return k(e)?i=e:(e=Vt(e),r=e.slice(0,-1),e=e[e.length-1]),Ge(t,(function(t){var s=i;if(!s){if(r&&r.length&&(t=zt(t,r)),null==t)return;s=t[e]}return null==s?s:s.apply(t,n)}))}));function sn(t,e){return Ge(t,Kt(e))}function on(t,e){return Qe(t,Ht(e))}function an(t,e,n){var r,i,s=-1/0,o=-1/0;if(null==e||"number"==typeof e&&"object"!=typeof t[0]&&null!=t)for(var a=0,l=(t=Se(t)?t:Dt(t)).length;a<l;a++)null!=(r=t[a])&&r>s&&(s=r);else e=Zt(e,n),Ke(t,(function(t,n,r){((i=e(t,n,r))>o||i===-1/0&&s===-1/0)&&(s=t,o=i)}));return s}function ln(t,e,n){var r,i,s=1/0,o=1/0;if(null==e||"number"==typeof e&&"object"!=typeof t[0]&&null!=t)for(var a=0,l=(t=Se(t)?t:Dt(t)).length;a<l;a++)null!=(r=t[a])&&r<s&&(s=r);else e=Zt(e,n),Ke(t,(function(t,n,r){((i=e(t,n,r))<o||i===1/0&&s===1/0)&&(s=t,o=i)}));return s}var cn=/[^\ud800-\udfff]|[\ud800-\udbff][\udc00-\udfff]|[\ud800-\udfff]/g;function un(t){return t?H(t)?u.call(t):F(t)?t.match(cn):Se(t)?Ge(t,Yt):Dt(t):[]}function dn(t,e,n){if(null==e||n)return Se(t)||(t=Dt(t)),t[ne(t.length-1)];var r=un(t),i=st(r);e=Math.max(Math.min(e,i),0);for(var s=i-1,o=0;o<e;o++){var a=ne(o,s),l=r[o];r[o]=r[a],r[a]=l}return r.slice(0,e)}function hn(t){return dn(t,1/0)}function fn(t,e,n){var r=0;return e=Zt(e,n),sn(Ge(t,(function(t,n,i){return{value:t,index:r++,criteria:e(t,n,i)}})).sort((function(t,e){var n=t.criteria,r=e.criteria;if(n!==r){if(n>r||void 0===n)return 1;if(n<r||void 0===r)return-1}return t.index-e.index})),"value")}function pn(t,e){return function(n,r,i){var s=e?[[],[]]:{};return r=Zt(r,i),Ke(n,(function(e,i){var o=r(e,i,n);t(s,e,o)})),s}}const yn=pn((function(t,e,n){K(t,n)?t[n].push(e):t[n]=[e]})),bn=pn((function(t,e,n){t[n]=e})),_n=pn((function(t,e,n){K(t,n)?t[n]++:t[n]=1})),mn=pn((function(t,e,n){t[n?0:1].push(e)}),!0);function gn(t){return null==t?0:Se(t)?t.length:at(t).length}function vn(t,e,n){return e in n}const wn=A((function(t,e){var n={},r=e[0];if(null==t)return n;k(r)?(e.length>1&&(r=Gt(r,e[1])),e=bt(t)):(r=vn,e=Ae(e,!1,!1),t=Object(t));for(var i=0,s=e.length;i<s;i++){var o=e[i],a=t[o];r(a,o,t)&&(n[o]=a)}return n})),In=A((function(t,e){var n,r=e[0];return k(r)?(r=Le(r),e.length>1&&(n=e[1])):(e=Ge(Ae(e,!1,!1),String),r=function(t,n){return!nn(e,n)}),wn(t,r,n)}));function Sn(t,e,n){return u.call(t,0,Math.max(0,t.length-(null==e||n?1:e)))}function An(t,e,n){return null==t||t.length<1?null==e||n?void 0:[]:null==e||n?t[0]:Sn(t,t.length-e)}function Tn(t,e,n){return u.call(t,null==e||n?1:e)}function On(t,e,n){return null==t||t.length<1?null==e||n?void 0:[]:null==e||n?t[t.length-1]:Tn(t,Math.max(0,t.length-e))}function Bn(t){return Qe(t,Boolean)}function Dn(t,e){return Ae(t,e,!1)}const Mn=A((function(t,e){return e=Ae(e,!0,!0),Qe(t,(function(t){return!nn(e,t)}))})),xn=A((function(t,e){return Mn(t,e)}));function Fn(t,e,n,r){D(e)||(r=n,n=e,e=!1),null!=n&&(n=Zt(n,r));for(var i=[],s=[],o=0,a=st(t);o<a;o++){var l=t[o],c=n?n(l,o,t):l;e&&!n?(o&&s===c||i.push(l),s=c):n?nn(s,c)||(s.push(c),i.push(l)):nn(i,l)||i.push(l)}return i}const Ln=A((function(t){return Fn(Ae(t,!0,!0))}));function Nn(t){for(var e=[],n=arguments.length,r=0,i=st(t);r<i;r++){var s=t[r];if(!nn(e,s)){var o;for(o=1;o<n&&nn(arguments[o],s);o++);o===n&&e.push(s)}}return e}function Un(t){for(var e=t&&an(t,st).length||0,n=Array(e),r=0;r<e;r++)n[r]=sn(t,r);return n}const Cn=A(Un);function En(t,e){for(var n={},r=0,i=st(t);r<i;r++)e?n[t[r]]=e[r]:n[t[r][0]]=t[r][1];return n}function jn(t,e,n){null==e&&(e=t||0,t=0),n||(n=e<t?-1:1);for(var r=Math.max(Math.ceil((e-t)/n),0),i=Array(r),s=0;s<r;s++,t+=n)i[s]=t;return i}function Rn(t,e){if(null==e||e<1)return[];for(var n=[],r=0,i=t.length;r<i;)n.push(u.call(t,r,r+=e));return n}function Pn(t,e){return t._chain?ut(e).chain():e}function kn(t){return Ke(Ft(t),(function(e){var n=ut[e]=t[e];ut.prototype[e]=function(){var t=[this._wrapped];return c.apply(t,arguments),Pn(this,n.apply(ut,t))}})),ut}Ke(["pop","push","reverse","shift","sort","splice","unshift"],(function(t){var e=o[t];ut.prototype[t]=function(){var n=this._wrapped;return null!=n&&(e.apply(n,arguments),"shift"!==t&&"splice"!==t||0!==n.length||delete n[0]),Pn(this,n)}})),Ke(["concat","join","slice"],(function(t){var e=o[t];ut.prototype[t]=function(){var t=this._wrapped;return null!=t&&(t=e.apply(t,arguments)),Pn(this,t)}}));const Vn=ut;var zn=kn(r);zn._=zn},904:(t,e,n)=>{t.exports=n(739)}},__webpack_module_cache__={};function __webpack_require__(t){var e=__webpack_module_cache__[t];if(void 0!==e)return e.exports;var n=__webpack_module_cache__[t]={exports:{}};return __webpack_modules__[t](n,n.exports,__webpack_require__),n.exports}__webpack_require__.d=(t,e)=>{for(var n in e)__webpack_require__.o(e,n)&&!__webpack_require__.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:e[n]})},__webpack_require__.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),__webpack_require__.r=t=>{"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})};var __webpack_exports__={},streamlit_component_lib__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__(416),underscore__WEBPACK_IMPORTED_MODULE_1__=__webpack_require__(787),_circle_to_polygon__WEBPACK_IMPORTED_MODULE_2__=__webpack_require__(453),ignore_render=!1;function onMapClick(t){window.__GLOBAL_DATA__.lat_lng_clicked=t.latlng,debouncedUpdateComponentValue(window.map)}let debouncedUpdateComponentValue=(0,underscore__WEBPACK_IMPORTED_MODULE_1__.sg)(updateComponentValue,250);function updateComponentValue(t){const e=window.__GLOBAL_DATA__;let n=e.previous_data,r=t.getBounds(),i=t.getZoom(),s={last_clicked:e.lat_lng_clicked,last_object_clicked:e.last_object_clicked,last_object_clicked_tooltip:e.last_object_clicked_tooltip,last_object_clicked_popup:e.last_object_clicked_popup,all_drawings:e.all_drawings,last_active_drawing:e.last_active_drawing,bounds:r,zoom:i,last_circle_radius:e.last_circle_radius,last_circle_polygon:e.last_circle_polygon,center:t.getCenter(),selected_layers:Object.values(e.selected_layers)},o={};o=e.returned_objects?Object.fromEntries(Object.entries(s).filter((t=>{let[n]=t;return e.returned_objects.includes(n)}))):s,JSON.stringify(n)!==JSON.stringify(o)&&(e.previous_data=o,streamlit_component_lib__WEBPACK_IMPORTED_MODULE_0__.wk.setComponentValue(o))}function onMapMove(t){debouncedUpdateComponentValue(window.map)}function extractContent(t){var e=document.createElement("span");return e.innerHTML=t,(e.textContent||e.innerText).trim()}function onDraw(t){const e=window.__GLOBAL_DATA__;var n=t.layerType,r=t.layer;if("circle"===n){var i=[r._latlng.lng,r._latlng.lat],s=r.options.radius,o=(0,_circle_to_polygon__WEBPACK_IMPORTED_MODULE_2__.K)(i,s),a=t.layer.feature=t.layer.feature||{};a.type="Feature",a.properties=a.properties||{},a.properties.radius=s,e.last_circle_radius=s/1e3,e.last_circle_polygon=o}return onLayerClick(t)}function removeLayer(t){const e=window.__GLOBAL_DATA__;let n=t.layer;if(n&&n._url&&n.wmsParams&&n.wmsParams.layers){const t=n.wmsParams.layers,r=`${n._url},${t}`;e.selected_layers[r]&&delete e.selected_layers[r]}debouncedUpdateComponentValue(window.map)}function addLayer(t){const e=window.__GLOBAL_DATA__;let n=t.layer;if(n&&n._url&&n.wmsParams&&n.wmsParams.layers){const t=n.wmsParams.layers,r=n._url,i=`${r},${t}`;e.selected_layers[i]||(e.selected_layers[i]={name:t,url:r})}debouncedUpdateComponentValue(window.map)}function onLayerClick(t){const e=window.__GLOBAL_DATA__;if(e.last_object_clicked=t.latlng,t.sourceTarget._tooltip&&t.sourceTarget._tooltip._content){let n=extractContent(t.sourceTarget.getTooltip().getContent());e.last_object_clicked_tooltip=n}else if(t.target._tooltip&&t.target._tooltip._content){let n=t.target.getTooltip().getContent()(t.sourceTarget).innerText;e.last_object_clicked_tooltip=n}if(t.sourceTarget._popup&&t.sourceTarget._popup._content){let n=t.sourceTarget.getPopup().getContent().innerText;e.last_object_clicked_popup=n}else if(t.target._popup&&t.target._popup._content){let n=t.target.getPopup().getContent()(t.sourceTarget).innerText;e.last_object_clicked_popup=n}let n=[];t.layer&&t.layer.toGeoJSON&&(e.last_active_drawing=t.layer.toGeoJSON()),window.drawnItems.toGeoJSON&&(n=window.drawnItems.toGeoJSON().features),e.all_drawings=n,debouncedUpdateComponentValue(window.map)}function getPixelatedStyles(t){if(t){return"\n    .leaflet-image-layer {\n      /* old android/safari*/\n      image-rendering: -webkit-optimize-contrast;\n      image-rendering: crisp-edges; /* safari */\n      image-rendering: pixelated; /* chrome */\n      image-rendering: -moz-crisp-edges; /* firefox */\n      image-rendering: -o-crisp-edges; /* opera */\n      -ms-interpolation-mode: nearest-neighbor; /* ie */\n    }\n    "}return"\n  .leaflet-image-layer {\n  }\n  "}async function onRender(event){const data=event.detail,script=data.args.script,height=data.args.height,width=data.args.width,html=data.args.html,header=data.args.header,js_links=data.args.js_links,css_links=data.args.css_links,returned_objects=data.args.returned_objects,_default=data.args.default,zoom=data.args.zoom,center=data.args.center,feature_group=data.args.feature_group,return_on_hover=data.args.return_on_hover,layer_control=data.args.layer_control,pixelated=data.args.pixelated,loadScripts=async()=>{ignore_render=!0;for(const e of js_links)await new Promise(((t,n)=>{const r=document.createElement("script");r.src=e,r.async=!1,r.onload=t,r.onerror=n,window.document.body.appendChild(r)}));css_links.forEach((t=>{const e=document.createElement("link");e.rel="stylesheet",e.href=t,window.document.head.appendChild(e)}));const t=document.createElement("style");t.innerHTML=getPixelatedStyles(pixelated),window.document.head.appendChild(t),window.document.head.innerHTML+=header},finalizeOnRender=()=>{if(window.map){if(feature_group!==window.__GLOBAL_DATA__.last_feature_group||layer_control!==window.__GLOBAL_DATA__.last_layer_control)if(window.feature_group&&window.feature_group.length>0&&window.feature_group.forEach((t=>{window.map.removeLayer(t)})),window.layer_control&&window.map.removeControl(window.layer_control),window.__GLOBAL_DATA__.last_feature_group=feature_group,window.__GLOBAL_DATA__.last_layer_control=layer_control,feature_group){eval(feature_group+layer_control);for(let t in window.map._layers){let e=window.map._layers[t];e.off("click",onLayerClick),e.on("click",onLayerClick),return_on_hover&&(e.off("mouseover",onLayerClick),e.on("mouseover",onLayerClick))}}else eval(layer_control);var view_changed=!1,new_zoom=window.map.getZoom();zoom&&zoom!==window.__GLOBAL_DATA__.last_zoom&&(new_zoom=zoom,window.__GLOBAL_DATA__.last_zoom=zoom,view_changed=!0);var new_center=window.map.getCenter();center&&JSON.stringify(center)!==JSON.stringify(window.__GLOBAL_DATA__.last_center)&&(new_center=center,window.__GLOBAL_DATA__.last_center=center,view_changed=!0),view_changed&&window.map.setView(new_center,new_zoom)}};if(!window.map&&!1===ignore_render){const t=document.getElementById("parent"),e=document.getElementById("map_div"),n=document.getElementById("map_div2");n&&(n.style.height=`${height}px`,n.style.width=`${width}px`),e&&(e.style.height=`${height}px`,e.style.width=`${width}px`,window.__GLOBAL_DATA__={lat_lng_clicked:null,last_object_clicked:null,last_object_clicked_tooltip:null,last_object_clicked_popup:null,all_drawings:null,last_active_drawing:null,zoom:null,last_circle_radius:null,last_circle_polygon:null,returned_objects:returned_objects,previous_data:_default,last_zoom:null,last_center:null,last_feature_group:null,last_layer_control:null,selected_layers:{}},-1!==script.indexOf("map_div2")&&(null===t||void 0===t||t.classList.remove("single"),null===t||void 0===t||t.classList.add("double"))),await loadScripts().then((()=>{ignore_render=!1;const t=document.createElement("script");if(!window.map){const n=document.createElement("div");n.innerHTML=html,document.body.appendChild(n),t.innerHTML=script+`window.map = map_div; window.initComponent(map_div, ${return_on_hover});`,document.body.appendChild(t);const r=getPixelatedStyles(pixelated);var e=document.createElement("style");e.innerText=r,document.head.appendChild(e)}finalizeOnRender()}))}finalizeOnRender(),streamlit_component_lib__WEBPACK_IMPORTED_MODULE_0__.wk.setFrameHeight()}window.Streamlit=streamlit_component_lib__WEBPACK_IMPORTED_MODULE_0__.wk,window.initComponent=(t,e)=>{const n=window.__GLOBAL_DATA__;t.on("click",onMapClick),t.on("moveend",onMapMove);for(let r in t._layers){let i=t._layers[r];if(i&&i._url&&i.wmsParams&&i.wmsParams.layers){const t=i.wmsParams.layers,e=i._url,r=`${e},${t}`;n.selected_layers[r]||(n.selected_layers[r]={name:t,url:e})}i.on("click",onLayerClick),e&&i.on("mouseover",onLayerClick)}t.on("draw:created",onDraw),t.on("draw:edited",onDraw),t.on("draw:deleted",onDraw),t.on("overlayadd",addLayer),t.on("overlayremove",removeLayer),streamlit_component_lib__WEBPACK_IMPORTED_MODULE_0__.wk.setFrameHeight(),updateComponentValue(t)},streamlit_component_lib__WEBPACK_IMPORTED_MODULE_0__.wk.events.addEventListener(streamlit_component_lib__WEBPACK_IMPORTED_MODULE_0__.wk.RENDER_EVENT,onRender),streamlit_component_lib__WEBPACK_IMPORTED_MODULE_0__.wk.setComponentReady(),streamlit_component_lib__WEBPACK_IMPORTED_MODULE_0__.wk.setFrameHeight()})();
//# sourceMappingURL=main.76438bd2.js.map