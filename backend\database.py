from sqlalchemy.orm import Session
from models import Doctor, Hospital, SearchHistory
from geopy.distance import geodesic
from typing import List, Optional, Dict
import json
from location_service import LocationService

class DatabaseOperations:
    def __init__(self, db: Session):
        self.db = db
        self.location_service = LocationService()
    
    def add_sample_data(self):
        """Add sample doctors and hospitals for testing"""
        # Sample doctors data
        sample_doctors = [
            # Delhi
            {
                "name": "Dr. <PERSON>",
                "specialization": "Cardiology",
                "hospital": "Apollo Hospital",
                "address": "Sarita Vihar, New Delhi",
                "latitude": 28.5355,
                "longitude": 77.2910,
                "phone": "+91-11-2692-5858",
                "rating": 4.8,
                "years_experience": 15
            },
            {
                "name": "Dr. <PERSON><PERSON>",
                "specialization": "Neurology",
                "hospital": "AIIMS",
                "address": "Ansari Nagar, New Delhi",
                "latitude": 28.5672,
                "longitude": 77.2100,
                "phone": "+91-11-2658-8500",
                "rating": 4.9,
                "years_experience": 20
            },
            # Mumbai
            {
                "name": "<PERSON><PERSON> <PERSON><PERSON>",
                "specialization": "Pediatrics",
                "hospital": "Kokilaben Hospital",
                "address": "Andheri West, Mumbai",
                "latitude": 19.1136,
                "longitude": 72.8697,
                "phone": "+91-22-4269-8888",
                "rating": 4.6,
                "years_experience": 12
            },
            {
                "name": "Dr. Ravi Mehta",
                "specialization": "Orthopedics",
                "hospital": "Lilavati Hospital",
                "address": "Bandra West, Mumbai",
                "latitude": 19.0596,
                "longitude": 72.8295,
                "phone": "+91-22-2675-1000",
                "rating": 4.7,
                "years_experience": 18
            },
            # Bangalore
            {
                "name": "Dr. Amit Singh",
                "specialization": "General Medicine",
                "hospital": "Manipal Hospital",
                "address": "HAL Airport Road, Bangalore",
                "latitude": 12.9716,
                "longitude": 77.5946,
                "phone": "+91-80-2502-4444",
                "rating": 4.5,
                "years_experience": 10
            }
        ]
        
        # Check if data already exists
        if self.db.query(Doctor).count() == 0:
            for doctor_data in sample_doctors:
                doctor = Doctor(**doctor_data)
                self.db.add(doctor)
            self.db.commit()
    
    def find_doctors_by_location(self, user_lat: float, user_lon: float, radius_km: float = 10.0) -> List[Doctor]:
        """Find doctors within specified radius"""
        all_doctors = self.db.query(Doctor).filter(Doctor.is_available == True).all()
        nearby_doctors = []
        
        user_location = (user_lat, user_lon)
        
        for doctor in all_doctors:
            doctor_location = (doctor.latitude, doctor.longitude)
            distance = geodesic(user_location, doctor_location).kilometers
            
            if distance <= radius_km:
                # Add distance as an attribute for sorting
                doctor.distance_km = round(distance, 2)
                nearby_doctors.append(doctor)
        
        # Sort by distance
        nearby_doctors.sort(key=lambda x: x.distance_km)
        return nearby_doctors
    
    def find_doctors_by_specialization(self, specialization: str, user_lat: float, user_lon: float, radius_km: float = 10.0) -> List[Doctor]:
        """Find doctors by specialization within radius"""
        doctors = self.find_doctors_by_location(user_lat, user_lon, radius_km)
        return [d for d in doctors if specialization.lower() in d.specialization.lower()]
    
    def save_search_history(self, user_lat: float, user_lon: float, query: str, results_count: int):
        """Save search history for analytics"""
        search_record = SearchHistory(
            user_latitude=user_lat,
            user_longitude=user_lon,
            query=query,
            results_count=results_count
        )
        self.db.add(search_record)
        self.db.commit()
        return search_record

    def search_doctors_by_query(self, query: str, user_lat: float, user_lon: float, radius_km: float = 10.0) -> List[Doctor]:
        """Intelligent search based on natural language query"""
        query_lower = query.lower()

        # Extract specialization keywords
        specialization_keywords = {
            'heart': 'cardiology',
            'cardiac': 'cardiology',
            'cardiologist': 'cardiology',
            'children': 'pediatrics',
            'child': 'pediatrics',
            'pediatric': 'pediatrics',
            'pediatrician': 'pediatrics',
            'skin': 'dermatology',
            'dermatologist': 'dermatology',
            'bone': 'orthopedics',
            'joint': 'orthopedics',
            'orthopedic': 'orthopedics',
            'brain': 'neurology',
            'neurologist': 'neurology',
            'nerve': 'neurology'
        }

        # Find matching specialization
        target_specialization = None
        for keyword, specialization in specialization_keywords.items():
            if keyword in query_lower:
                target_specialization = specialization
                break

        # Get doctors within radius
        nearby_doctors = self.find_doctors_by_location(user_lat, user_lon, radius_km)

        # Filter by specialization if found
        if target_specialization:
            filtered_doctors = [d for d in nearby_doctors if target_specialization.lower() in d.specialization.lower()]
            return filtered_doctors if filtered_doctors else nearby_doctors

        return nearby_doctors

    def get_doctor_by_id(self, doctor_id: int) -> Optional[Doctor]:
        """Get specific doctor by ID"""
        return self.db.query(Doctor).filter(Doctor.id == doctor_id).first()

    def get_specializations(self) -> List[str]:
        """Get all unique specializations"""
        specializations = self.db.query(Doctor.specialization).distinct().all()
        return [spec[0] for spec in specializations]

    def get_hospitals(self) -> List[Hospital]:
        """Get all hospitals"""
        return self.db.query(Hospital).all()

    def add_doctor(self, doctor_data: Dict) -> Doctor:
        """Add new doctor to database"""
        doctor = Doctor(**doctor_data)
        self.db.add(doctor)
        self.db.commit()
        self.db.refresh(doctor)
        return doctor

    def update_doctor_rating(self, doctor_id: int, new_rating: float) -> bool:
        """Update doctor's rating"""
        doctor = self.get_doctor_by_id(doctor_id)
        if doctor:
            doctor.rating = new_rating
            self.db.commit()
            return True
        return False
