#!/usr/bin/env python3
"""
HealNav Frontend Startup Script
"""
import os
import sys
import subprocess
import time
import requests
from pathlib import Path

def check_backend_health():
    """Check if backend is running"""
    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
        return response.status_code == 200
    except:
        return False

def wait_for_backend(max_wait=30):
    """Wait for backend to be ready"""
    print("⏳ Waiting for backend to be ready...")
    for i in range(max_wait):
        if check_backend_health():
            print("✅ Backend is ready!")
            return True
        time.sleep(1)
        print(f"   Waiting... ({i+1}/{max_wait})")
    return False

def start_frontend():
    """Start the Streamlit frontend"""
    print("🚀 Starting HealNav Frontend...")
    
    # Change to frontend directory
    frontend_dir = Path(__file__).parent / "frontend"
    os.chdir(frontend_dir)
    
    # Start streamlit
    try:
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", "app.py",
            "--server.port", "8501",
            "--server.address", "localhost"
        ], check=True)
    except KeyboardInterrupt:
        print("\n🛑 Frontend stopped")
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to start frontend: {e}")

if __name__ == "__main__":
    print("🏥 HealNav Frontend Startup")
    print("=" * 40)
    
    # Check if backend is running
    if not check_backend_health():
        print("⚠️  Backend is not running!")
        print("Please start the backend first:")
        print("   python start_backend.py")
        print("\nOr start both together:")
        print("   python start_app.py")
        
        # Ask if user wants to continue anyway
        response = input("\nContinue anyway? (y/N): ").lower()
        if response != 'y':
            sys.exit(1)
    
    start_frontend()
