#!/usr/bin/env python3
"""
HealNav Complete Application Startup Script
Starts both backend and frontend
"""
import os
import sys
import subprocess
import time
import threading
from pathlib import Path

def start_backend():
    """Start backend in a separate process"""
    print("🚀 Starting Backend Server...")
    backend_dir = Path(__file__).parent / "backend"
    
    try:
        subprocess.run([
            sys.executable, "-m", "uvicorn", 
            "main:app", 
            "--host", "0.0.0.0", 
            "--port", "8000", 
            "--reload"
        ], cwd=backend_dir, check=True)
    except KeyboardInterrupt:
        print("\n🛑 Backend stopped")
    except Exception as e:
        print(f"❌ Backend error: {e}")

def start_frontend():
    """Start frontend in a separate process"""
    print("🚀 Starting Frontend...")
    frontend_dir = Path(__file__).parent / "frontend"
    
    # Wait a bit for backend to start
    time.sleep(3)
    
    try:
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", "app.py",
            "--server.port", "8501",
            "--server.address", "localhost"
        ], cwd=frontend_dir, check=True)
    except KeyboardInterrupt:
        print("\n🛑 Frontend stopped")
    except Exception as e:
        print(f"❌ Frontend error: {e}")

def main():
    print("🏥 HealNav Complete Application Startup")
    print("=" * 50)
    print("Starting both Backend and Frontend...")
    print("Backend will run on: http://localhost:8000")
    print("Frontend will run on: http://localhost:8501")
    print("=" * 50)
    
    # Start backend in a thread
    backend_thread = threading.Thread(target=start_backend, daemon=True)
    backend_thread.start()
    
    # Start frontend in main thread
    try:
        start_frontend()
    except KeyboardInterrupt:
        print("\n🛑 Application stopped")

if __name__ == "__main__":
    main()
