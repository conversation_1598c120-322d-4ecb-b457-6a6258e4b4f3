#!/usr/bin/env python3
"""
HealNav API Testing Script
"""
import requests
import json
import time

API_BASE_URL = "http://localhost:8000"

def test_health_check():
    """Test API health endpoint"""
    print("🔍 Testing health check...")
    try:
        response = requests.get(f"{API_BASE_URL}/health", timeout=5)
        if response.status_code == 200:
            print("✅ Health check passed")
            return True
        else:
            print(f"❌ Health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Health check error: {e}")
        return False

def test_search_api():
    """Test search functionality"""
    print("🔍 Testing search API...")
    
    test_data = {
        "latitude": 40.7128,
        "longitude": -74.0060,
        "query": "I need a cardiologist for chest pain",
        "radius_km": 10.0
    }
    
    try:
        response = requests.post(f"{API_BASE_URL}/search", json=test_data, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Search API test passed")
            print(f"   Found {data.get('total_found', 0)} doctors")
            print(f"   AI Recommendation: {data.get('ai_recommendation', 'N/A')[:100]}...")
            return True
        else:
            print(f"❌ Search API failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Search API error: {e}")
        return False

def test_doctors_api():
    """Test doctors listing API"""
    print("🔍 Testing doctors API...")
    
    try:
        response = requests.get(f"{API_BASE_URL}/doctors", timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Doctors API test passed")
            print(f"   Found {len(data)} doctors in database")
            return True
        else:
            print(f"❌ Doctors API failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Doctors API error: {e}")
        return False

def test_specializations_api():
    """Test specializations API"""
    print("🔍 Testing specializations API...")
    
    try:
        response = requests.get(f"{API_BASE_URL}/specializations", timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Specializations API test passed")
            print(f"   Available specializations: {data.get('specializations', [])}")
            return True
        else:
            print(f"❌ Specializations API failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Specializations API error: {e}")
        return False

def test_analyze_api():
    """Test query analysis API"""
    print("🔍 Testing analyze API...")
    
    try:
        response = requests.post(
            f"{API_BASE_URL}/analyze", 
            params={"query": "I have chest pain and need help"},
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Analyze API test passed")
            print(f"   Service used: {data.get('service', 'unknown')}")
            print(f"   Analysis: {json.dumps(data.get('analysis', {}), indent=2)}")
            return True
        else:
            print(f"❌ Analyze API failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Analyze API error: {e}")
        return False

def main():
    print("🏥 HealNav API Testing Suite")
    print("=" * 40)
    
    tests = [
        test_health_check,
        test_doctors_api,
        test_specializations_api,
        test_search_api,
        test_analyze_api
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            print()  # Empty line between tests
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            print()
    
    print("=" * 40)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! HealNav API is working correctly.")
    else:
        print("⚠️  Some tests failed. Please check the backend server.")

if __name__ == "__main__":
    main()
