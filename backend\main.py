from fastapi import FastAP<PERSON>, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List, Optional
from sqlalchemy.orm import Session
import os
from dotenv import load_dotenv

from models import create_tables, get_db, Doctor
from database import DatabaseOperations
from agent_system import HealNavAgent, format_doctor_recommendation
from groq_service import GroqService

# Load environment variables
load_dotenv()

app = FastAPI(title="HealNav API", description="AI-powered medical facility finder", version="1.0.0")

# Create database tables
create_tables()

# Initialize AI services
agent = HealNavAgent()
groq_service = GroqService()

# CORS middleware for frontend communication
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:8501"],  # Streamlit default port
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Pydantic models
class LocationRequest(BaseModel):
    latitude: float
    longitude: float
    query: Optional[str] = None
    radius_km: Optional[float] = 10.0

class DoctorInfo(BaseModel):
    id: int
    name: str
    specialization: str
    hospital: str
    address: str
    latitude: float
    longitude: float
    distance_km: float
    phone: Optional[str] = None
    rating: Optional[float] = None

class SearchResponse(BaseModel):
    doctors: List[DoctorInfo]
    ai_recommendation: str
    total_found: int

@app.get("/")
async def root():
    return {"message": "HealNav API is running", "status": "healthy"}

@app.get("/health")
async def health_check():
    return {"status": "healthy", "service": "HealNav API"}

@app.post("/search", response_model=SearchResponse)
async def search_medical_facilities(request: LocationRequest, db: Session = Depends(get_db)):
    """Intelligent search for medical facilities using AI"""
    try:
        db_ops = DatabaseOperations(db)

        # Add sample data if database is empty
        db_ops.add_sample_data()

        # Use intelligent search if query provided, otherwise location-based
        if request.query:
            doctors = db_ops.search_doctors_by_query(
                request.query,
                request.latitude,
                request.longitude,
                request.radius_km
            )
        else:
            doctors = db_ops.find_doctors_by_location(
                request.latitude,
                request.longitude,
                request.radius_km
            )

        # Convert to response format
        doctor_list = []
        doctor_dicts = []  # For AI processing

        for doctor in doctors:
            doctor_info = DoctorInfo(
                id=doctor.id,
                name=doctor.name,
                specialization=doctor.specialization,
                hospital=doctor.hospital,
                address=doctor.address,
                latitude=doctor.latitude,
                longitude=doctor.longitude,
                distance_km=doctor.distance_km,
                phone=doctor.phone,
                rating=doctor.rating
            )
            doctor_list.append(doctor_info)

            # For AI processing
            doctor_dicts.append({
                "id": doctor.id,
                "name": doctor.name,
                "specialization": doctor.specialization,
                "hospital": doctor.hospital,
                "distance_km": doctor.distance_km,
                "rating": doctor.rating
            })

        # Simple precaution message
        precaution = "Consult with a qualified doctor for proper diagnosis. Follow prescribed medications and maintain healthy lifestyle."

        # Save search history
        db_ops.save_search_history(
            request.latitude,
            request.longitude,
            request.query or "Location-based search",
            len(doctor_list)
        )

        return SearchResponse(
            doctors=doctor_list,
            ai_recommendation=precaution,
            total_found=len(doctor_list)
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Search failed: {str(e)}")

@app.get("/doctors", response_model=List[DoctorInfo])
async def get_all_doctors(db: Session = Depends(get_db)):
    """Get all available doctors"""
    try:
        db_ops = DatabaseOperations(db)
        db_ops.add_sample_data()  # Ensure sample data exists

        doctors = db.query(Doctor).filter(Doctor.is_available == True).all()
        doctor_list = []

        for doctor in doctors:
            doctor_info = DoctorInfo(
                id=doctor.id,
                name=doctor.name,
                specialization=doctor.specialization,
                hospital=doctor.hospital,
                address=doctor.address,
                latitude=doctor.latitude,
                longitude=doctor.longitude,
                distance_km=0.0,  # Distance not calculated for general list
                phone=doctor.phone,
                rating=doctor.rating
            )
            doctor_list.append(doctor_info)

        return doctor_list

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to fetch doctors: {str(e)}")

@app.get("/specializations")
async def get_specializations(db: Session = Depends(get_db)):
    """Get all available medical specializations"""
    try:
        db_ops = DatabaseOperations(db)
        db_ops.add_sample_data()  # Ensure sample data exists
        specializations = db_ops.get_specializations()
        return {"specializations": specializations}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to fetch specializations: {str(e)}")

@app.post("/analyze")
async def analyze_query(query: str):
    """Analyze medical query using AI (for testing purposes)"""
    try:
        if groq_service.is_available():
            analysis = groq_service.analyze_medical_query(query, {"latitude": 40.7128, "longitude": -74.0060})
            return {"analysis": analysis, "service": "groq"}
        else:
            agent_result = agent.process_query(query, {"latitude": 40.7128, "longitude": -74.0060})
            return {"analysis": agent_result, "service": "langgraph"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Analysis failed: {str(e)}")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
