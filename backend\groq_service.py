import os
from groq import <PERSON>roq
from typing import Dict, List, Optional, Any
import json
from dotenv import load_dotenv

load_dotenv()

class GroqService:
    def __init__(self):
        self.api_key = os.getenv("GROQ_API_KEY")
        if not self.api_key:
            print("Warning: GROQ_API_KEY not found. Groq features will be disabled.")
            self.client = None
        else:
            self.client = Groq(api_key=self.api_key)
        
        self.model = "llama3-8b-8192"  # Fast and efficient model
    
    def is_available(self) -> bool:
        """Check if Groq service is available"""
        return self.client is not None
    
    def analyze_medical_query(self, user_query: str, user_location: Dict[str, float]) -> Dict[str, Any]:
        """Analyze medical query using Groq for enhanced understanding"""
        if not self.is_available():
            return self._fallback_analysis(user_query)
        
        prompt = f"""
        You are a medical AI assistant helping users find appropriate healthcare. 
        Analyze this medical query and provide structured information.
        
        User Query: "{user_query}"
        User Location: Latitude {user_location['latitude']}, Longitude {user_location['longitude']}
        
        Please analyze and return a JSON response with:
        1. "urgency_level": "low", "medium", "high", or "emergency"
        2. "specialization": the most relevant medical specialization
        3. "symptoms": list of symptoms mentioned
        4. "keywords": important medical keywords
        5. "recommendation_type": "immediate_care", "specialist", "general_practitioner", or "emergency"
        6. "explanation": brief explanation of your analysis
        
        Focus on medical accuracy and user safety. If symptoms suggest emergency, always recommend immediate care.
        
        Return only valid JSON format.
        """
        
        try:
            response = self.client.chat.completions.create(
                messages=[
                    {"role": "system", "content": "You are a medical AI assistant. Always prioritize user safety and provide accurate medical guidance."},
                    {"role": "user", "content": prompt}
                ],
                model=self.model,
                temperature=0.1,  # Low temperature for consistent medical advice
                max_tokens=500
            )
            
            content = response.choices[0].message.content
            
            # Try to parse JSON response
            try:
                analysis = json.loads(content)
                return analysis
            except json.JSONDecodeError:
                # If JSON parsing fails, extract information manually
                return self._extract_from_text(content, user_query)
                
        except Exception as e:
            print(f"Groq API error: {e}")
            return self._fallback_analysis(user_query)
    
    def generate_personalized_recommendation(self, doctors: List[Dict], 
                                           user_query: str, 
                                           analysis: Dict[str, Any]) -> str:
        """Generate personalized recommendation using Groq"""
        if not self.is_available():
            return self._fallback_recommendation(doctors, analysis)
        
        doctors_info = []
        for doctor in doctors[:5]:  # Top 5 doctors
            doctors_info.append({
                "name": doctor.get("name"),
                "specialization": doctor.get("specialization"),
                "distance": doctor.get("distance_km"),
                "rating": doctor.get("rating"),
                "hospital": doctor.get("hospital")
            })
        
        prompt = f"""
        Generate a helpful, personalized medical recommendation for a user.
        
        User Query: "{user_query}"
        Analysis: {json.dumps(analysis)}
        Available Doctors: {json.dumps(doctors_info)}
        
        Create a recommendation that:
        1. Addresses the user's specific concern
        2. Recommends the most appropriate doctor(s) from the list
        3. Explains why this doctor is suitable
        4. Includes urgency guidance if needed
        5. Is empathetic and reassuring
        
        Keep it concise but informative (max 200 words).
        Use a warm, professional tone.
        """
        
        try:
            response = self.client.chat.completions.create(
                messages=[
                    {"role": "system", "content": "You are a helpful medical assistant providing personalized healthcare recommendations."},
                    {"role": "user", "content": prompt}
                ],
                model=self.model,
                temperature=0.3,
                max_tokens=300
            )
            
            return response.choices[0].message.content
            
        except Exception as e:
            print(f"Groq recommendation error: {e}")
            return self._fallback_recommendation(doctors, analysis)
    
    def extract_medical_entities(self, text: str) -> Dict[str, List[str]]:
        """Extract medical entities from text using Groq"""
        if not self.is_available():
            return {"symptoms": [], "conditions": [], "specializations": []}
        
        prompt = f"""
        Extract medical entities from this text: "{text}"
        
        Return a JSON with:
        - "symptoms": list of symptoms mentioned
        - "conditions": list of medical conditions mentioned  
        - "specializations": list of medical specializations mentioned
        
        Return only valid JSON.
        """
        
        try:
            response = self.client.chat.completions.create(
                messages=[
                    {"role": "user", "content": prompt}
                ],
                model=self.model,
                temperature=0.1,
                max_tokens=200
            )
            
            content = response.choices[0].message.content
            return json.loads(content)
            
        except Exception as e:
            print(f"Entity extraction error: {e}")
            return {"symptoms": [], "conditions": [], "specializations": []}
    
    def _fallback_analysis(self, user_query: str) -> Dict[str, Any]:
        """Fallback analysis when Groq is not available"""
        query_lower = user_query.lower()
        
        # Simple keyword-based analysis
        urgency = "low"
        if any(word in query_lower for word in ["emergency", "urgent", "severe", "bleeding"]):
            urgency = "emergency"
        elif any(word in query_lower for word in ["pain", "fever", "infection"]):
            urgency = "high"
        elif any(word in query_lower for word in ["discomfort", "concern"]):
            urgency = "medium"
        
        # Simple specialization detection
        specialization = "general"
        spec_map = {
            "heart": "cardiology",
            "child": "pediatrics", 
            "skin": "dermatology",
            "bone": "orthopedics",
            "brain": "neurology"
        }
        
        for keyword, spec in spec_map.items():
            if keyword in query_lower:
                specialization = spec
                break
        
        return {
            "urgency_level": urgency,
            "specialization": specialization,
            "symptoms": [],
            "keywords": query_lower.split(),
            "recommendation_type": "general_practitioner",
            "explanation": "Basic analysis (Groq not available)"
        }
    
    def _fallback_recommendation(self, doctors: List[Dict], analysis: Dict[str, Any]) -> str:
        """Fallback recommendation when Groq is not available"""
        if not doctors:
            return "No doctors found in your area. Please try expanding your search radius."
        
        recommendation = f"Based on your query, I found {len(doctors)} medical professionals nearby. "
        
        if analysis.get("urgency_level") == "emergency":
            recommendation = "⚠️ URGENT: Please seek immediate medical attention or call emergency services. "
        
        top_doctor = doctors[0]
        recommendation += f"I recommend starting with {top_doctor['name']}, a {top_doctor['specialization']} specialist "
        recommendation += f"located {top_doctor['distance_km']}km away at {top_doctor['hospital']}."
        
        return recommendation
    
    def _extract_from_text(self, text: str, original_query: str) -> Dict[str, Any]:
        """Extract information from Groq response text when JSON parsing fails"""
        # This is a simple fallback - in production you might want more sophisticated parsing
        return self._fallback_analysis(original_query)
