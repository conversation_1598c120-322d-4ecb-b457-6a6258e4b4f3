{"doctors": [{"name": "Dr. <PERSON><PERSON>", "speciality": "Cardiology", "hospital": "Apollo Hospital", "city": "New Delhi", "state": "Delhi", "address": "<PERSON>rita <PERSON>, New Delhi", "phone": "+91-11-2692-5858", "latitude": 28.5355, "longitude": 77.291, "rating": 4.8, "precaution": "Avoid smoking and maintain regular exercise. Monitor blood pressure daily."}, {"name": "Dr. <PERSON><PERSON>", "speciality": "Neurology", "hospital": "AIIMS", "city": "New Delhi", "state": "Delhi", "address": "Ansari Nagar, New Delhi", "phone": "+91-11-2658-8500", "latitude": 28.5672, "longitude": 77.21, "rating": 4.9, "precaution": "Get adequate sleep and avoid stress. Take prescribed medications on time."}, {"name": "Dr. <PERSON><PERSON>", "speciality": "Pediatrics", "hospital": "Kokilaben Hospital", "city": "Mumbai", "state": "Maharashtra", "address": "Andheri West, Mumbai", "phone": "+91-22-4269-8888", "latitude": 19.1136, "longitude": 72.8697, "rating": 4.6, "precaution": "Ensure proper vaccination schedule. Maintain good hygiene for children."}, {"name": "Dr. <PERSON>", "speciality": "Orthopedics", "hospital": "Lilavati Hospital", "city": "Mumbai", "state": "Maharashtra", "address": "Bandra West, Mumbai", "phone": "+91-22-2675-1000", "latitude": 19.0596, "longitude": 72.8295, "rating": 4.7, "precaution": "Avoid heavy lifting and maintain proper posture. Do regular stretching exercises."}, {"name": "Dr. <PERSON><PERSON>", "speciality": "General Medicine", "hospital": "Manipal Hospital", "city": "Bangalore", "state": "Karnataka", "address": "HAL Airport Road, Bangalore", "phone": "+91-80-2502-4444", "latitude": 12.9716, "longitude": 77.5946, "rating": 4.5, "precaution": "Regular health checkups and balanced diet. Stay hydrated and exercise regularly."}, {"name": "Dr. <PERSON><PERSON><PERSON>", "speciality": "Gynecology", "hospital": "Apollo Hospital", "city": "Bangalore", "state": "Karnataka", "address": "Bannerghatta Road, Bangalore", "phone": "+91-80-2630-0300", "latitude": 12.9352, "longitude": 77.6245, "rating": 4.8, "precaution": "Regular gynecological checkups and maintain personal hygiene. Eat iron-rich foods."}, {"name": "Dr. <PERSON><PERSON>", "speciality": "Cardiology", "hospital": "Apollo Hospital", "city": "Chennai", "state": "Tamil Nadu", "address": "Greams Lane, Chennai", "phone": "+91-44-2829-3333", "latitude": 13.0827, "longitude": 80.2707, "rating": 4.9, "precaution": "Control cholesterol levels and avoid fatty foods. Regular cardiac monitoring required."}, {"name": "Dr. <PERSON>", "speciality": "Dermatology", "hospital": "KIMS Hospital", "city": "Hyderabad", "state": "Telangana", "address": "Kondapur, Hyderabad", "phone": "+91-40-4488-5555", "latitude": 17.4399, "longitude": 78.3908, "rating": 4.6, "precaution": "Use sunscreen daily and avoid harsh chemicals. Keep skin moisturized and clean."}, {"name": "Dr. <PERSON><PERSON><PERSON>", "speciality": "ENT", "hospital": "Fortis Hospital", "city": "Gurgaon", "state": "Haryana", "address": "Sector 44, Gurgaon", "phone": "+91-************", "latitude": 28.4595, "longitude": 77.0266, "rating": 4.4, "precaution": "Avoid loud noises and keep ears clean. Don't use cotton swabs inside ears."}, {"name": "Dr. <PERSON><PERSON>", "speciality": "Ophthalmology", "hospital": "Sankara Nethralaya", "city": "Chennai", "state": "Tamil Nadu", "address": "Nungambakkam, Chennai", "phone": "+91-44-2827-1616", "latitude": 13.0569, "longitude": 80.2378, "rating": 4.7, "precaution": "Regular eye checkups and avoid screen strain. Use proper lighting while reading."}, {"name": "Dr. <PERSON><PERSON><PERSON>", "speciality": "Urology", "hospital": "Sterling Hospital", "city": "Ahmedabad", "state": "Gujarat", "address": "Gurukul Road, Ahmedabad", "phone": "+91-79-6677-0000", "latitude": 23.0225, "longitude": 72.5714, "rating": 4.5, "precaution": "Drink plenty of water and maintain good hygiene. Avoid holding urine for long periods."}, {"name": "Dr. <PERSON><PERSON>", "speciality": "Gastroenterology", "hospital": "Max Hospital", "city": "New Delhi", "state": "Delhi", "address": "Saket, New Delhi", "phone": "+91-11-2651-5050", "latitude": 28.5244, "longitude": 77.2066, "rating": 4.6, "precaution": "Eat fiber-rich foods and avoid spicy meals. Maintain regular meal timings."}, {"name": "Dr. <PERSON><PERSON><PERSON>", "speciality": "Psychiatry", "hospital": "<PERSON><PERSON><PERSON><PERSON>", "city": "Bangalore", "state": "Karnataka", "address": "Hosur Road, Bangalore", "phone": "+91-80-2699-5000", "latitude": 12.9431, "longitude": 77.5957, "rating": 4.8, "precaution": "Practice stress management and get adequate sleep. Maintain social connections."}, {"name": "Dr. <PERSON><PERSON>", "speciality": "Pulmonology", "hospital": "Care Hospital", "city": "Hyderabad", "state": "Telangana", "address": "Banjara Hills, Hyderabad", "phone": "+91-40-6165-6565", "latitude": 17.4126, "longitude": 78.4486, "rating": 4.4, "precaution": "Avoid smoking and pollution exposure. Practice breathing exercises regularly."}, {"name": "Dr. <PERSON><PERSON>", "speciality": "Cardiology", "hospital": "Fortis Hospital", "city": "Mumbai", "state": "Maharashtra", "address": "Mulund West, Mumbai", "phone": "+91-22-6754-4444", "latitude": 19.1728, "longitude": 72.9569, "rating": 4.7, "precaution": "Regular exercise and low-salt diet. Monitor blood pressure daily."}, {"name": "Dr. <PERSON>", "speciality": "Dermatology", "hospital": "Max Hospital", "city": "New Delhi", "state": "Delhi", "address": "Pitampura, New Delhi", "phone": "+91-11-4747-4747", "latitude": 28.6942, "longitude": 77.1325, "rating": 4.5, "precaution": "Use sunscreen daily and moisturize skin. Avoid harsh chemicals."}, {"name": "Dr. <PERSON>", "speciality": "Orthopedics", "hospital": "Manipal Hospital", "city": "Bangalore", "state": "Karnataka", "address": "Whitefield, Bangalore", "phone": "+91-80-6692-2222", "latitude": 12.9698, "longitude": 77.75, "rating": 4.6, "precaution": "Maintain good posture and do regular stretching. Avoid heavy lifting."}, {"name": "Dr. <PERSON><PERSON>", "speciality": "Neurology", "hospital": "Apollo Hospital", "city": "Chennai", "state": "Tamil Nadu", "address": "Vanagaram, Chennai", "phone": "+91-44-2829-4444", "latitude": 13.1067, "longitude": 80.1532, "rating": 4.8, "precaution": "Get adequate sleep and manage stress. Take medications on time."}, {"name": "Dr. <PERSON>", "speciality": "General Medicine", "hospital": "Yashoda Hospital", "city": "Hyderabad", "state": "Telangana", "address": "Somajiguda, Hyderabad", "phone": "+91-40-2344-4444", "latitude": 17.4239, "longitude": 78.4738, "rating": 4.3, "precaution": "Regular health checkups and balanced diet. Stay hydrated."}, {"name": "Dr. <PERSON><PERSON><PERSON><PERSON>", "speciality": "Pediatrics", "hospital": "Rainbow Hospital", "city": "Ahmedabad", "state": "Gujarat", "address": "Bopal, Ahmedabad", "phone": "+91-79-4030-4030", "latitude": 23.0395, "longitude": 72.4993, "rating": 4.7, "precaution": "Follow vaccination schedule and maintain hygiene. Balanced nutrition for children."}, {"name": "Dr. <PERSON><PERSON><PERSON>", "speciality": "Cardiology", "hospital": "Medanta Hospital", "city": "Gurgaon", "state": "Haryana", "address": "Sector 38, Gurgaon", "phone": "+91-************", "latitude": 28.4211, "longitude": 77.0437, "rating": 4.9, "precaution": "Control cholesterol and avoid smoking. Regular cardiac monitoring."}, {"name": "Dr. <PERSON>", "speciality": "Gynecology", "hospital": "Jaslok Hospital", "city": "Mumbai", "state": "Maharashtra", "address": "Pedder Road, Mumbai", "phone": "+91-22-6657-3333", "latitude": 18.9667, "longitude": 72.8081, "rating": 4.6, "precaution": "Regular gynecological checkups and maintain personal hygiene. Iron-rich diet."}, {"name": "Dr. <PERSON><PERSON><PERSON>", "speciality": "ENT", "hospital": "Sir Ganga Ram Hospital", "city": "New Delhi", "state": "Delhi", "address": "<PERSON>inder Nagar, New Delhi", "phone": "+91-11-2575-0000", "latitude": 28.6358, "longitude": 77.191, "rating": 4.4, "precaution": "Avoid loud noises and keep ears clean. Don't use cotton swabs inside ears."}, {"name": "Dr. <PERSON><PERSON>", "speciality": "Cardiology", "hospital": "Fortis Hospital", "city": "Bangalore", "state": "Karnataka", "address": "Bannerghatta Road, Bangalore", "phone": "+91-80-6621-4444", "latitude": 12.901, "longitude": 77.5963, "rating": 4.8, "precaution": "Regular exercise and heart-healthy diet. Monitor blood pressure."}, {"name": "Dr. <PERSON><PERSON>", "speciality": "Dermatology", "hospital": "Apollo Hospital", "city": "Hyderabad", "state": "Telangana", "address": "Jubilee Hills, Hyderabad", "phone": "+91-40-2360-7777", "latitude": 17.4274, "longitude": 78.4071, "rating": 4.5, "precaution": "Use gentle skincare products and avoid sun exposure. Stay hydrated."}, {"name": "Dr. <PERSON><PERSON>", "speciality": "Orthopedics", "hospital": "Hinduja Hospital", "city": "Mumbai", "state": "Maharashtra", "address": "Mahim, Mumbai", "phone": "+91-22-2445-2222", "latitude": 19.033, "longitude": 72.8397, "rating": 4.7, "precaution": "Maintain proper posture and do regular exercise. Avoid prolonged sitting."}, {"name": "Dr. <PERSON><PERSON>", "speciality": "Pediatrics", "hospital": "Fortis Hospital", "city": "Chennai", "state": "Tamil Nadu", "address": "Vadapalani, Chennai", "phone": "+91-44-6676-1000", "latitude": 13.0389, "longitude": 80.2095, "rating": 4.6, "precaution": "Follow immunization schedule and maintain child hygiene. Nutritious diet."}, {"name": "Dr. <PERSON><PERSON>", "speciality": "Neurology", "hospital": "Max Hospital", "city": "Gurgaon", "state": "Haryana", "address": "Golf Course Road, Gurgaon", "phone": "+91-************", "latitude": 28.4743, "longitude": 77.0668, "rating": 4.8, "precaution": "Adequate sleep and stress management. Regular neurological checkups."}, {"name": "Dr. <PERSON><PERSON>", "speciality": "Gynecology", "hospital": "Cloudnine Hospital", "city": "Bangalore", "state": "Karnataka", "address": "Jayanagar, Bangalore", "phone": "+91-80-4040-8888", "latitude": 12.9279, "longitude": 77.5937, "rating": 4.7, "precaution": "Regular health screenings and maintain menstrual hygiene. Calcium-rich diet."}, {"name": "Dr. <PERSON><PERSON>", "speciality": "General Medicine", "hospital": "BLK Hospital", "city": "New Delhi", "state": "Delhi", "address": "Pusa Road, New Delhi", "phone": "+91-11-3040-3040", "latitude": 28.6692, "longitude": 77.191, "rating": 4.4, "precaution": "Regular health checkups and balanced lifestyle. Avoid smoking."}, {"name": "Dr. <PERSON><PERSON><PERSON>", "speciality": "Ophthalmology", "hospital": "L V Prasad Eye Institute", "city": "Hyderabad", "state": "Telangana", "address": "Banjara Hills, Hyderabad", "phone": "+91-40-3061-8555", "latitude": 17.4065, "longitude": 78.4691, "rating": 4.9, "precaution": "Regular eye checkups and avoid screen strain. Use proper lighting."}, {"name": "Dr. <PERSON><PERSON>", "speciality": "Urology", "hospital": "Apollo Hospital", "city": "Ahmedabad", "state": "Gujarat", "address": "Gandhinagar Highway, Ahmedabad", "phone": "+91-79-6670-1111", "latitude": 23.0395, "longitude": 72.5664, "rating": 4.5, "precaution": "Drink plenty of water and maintain hygiene. Avoid holding urine."}]}