from langgraph.graph import StateGraph, END
from langchain.schema import BaseMessage, HumanMessage, AIMessage
from typing import TypedDict, List, Dict, Any, Optional
import json
from datetime import datetime

class AgentState(TypedDict):
    """State for the HealNav agent"""
    user_query: str
    user_location: Dict[str, float]  # {"latitude": float, "longitude": float}
    search_radius: float
    extracted_symptoms: List[str]
    extracted_specialization: str
    urgency_level: str  # "low", "medium", "high", "emergency"
    doctor_results: List[Dict]
    ai_recommendation: str
    conversation_history: List[BaseMessage]

class HealNavAgent:
    def __init__(self):
        self.graph = self._create_graph()
        
        # Medical specialization mapping
        self.specialization_map = {
            # Cardiovascular
            "chest pain": "cardiology",
            "heart": "cardiology",
            "cardiac": "cardiology",
            "blood pressure": "cardiology",
            
            # Pediatric
            "child": "pediatrics",
            "children": "pediatrics",
            "baby": "pediatrics",
            "infant": "pediatrics",
            
            # Dermatology
            "skin": "dermatology",
            "rash": "dermatology",
            "acne": "dermatology",
            "mole": "dermatology",
            
            # Orthopedics
            "bone": "orthopedics",
            "joint": "orthopedics",
            "fracture": "orthopedics",
            "back pain": "orthopedics",
            
            # Neurology
            "headache": "neurology",
            "brain": "neurology",
            "seizure": "neurology",
            "memory": "neurology",
            
            # Emergency keywords
            "emergency": "emergency",
            "urgent": "emergency",
            "severe pain": "emergency",
            "bleeding": "emergency",
            "unconscious": "emergency"
        }
        
        # Urgency keywords
        self.urgency_keywords = {
            "emergency": ["emergency", "urgent", "severe", "bleeding", "unconscious", "chest pain", "difficulty breathing"],
            "high": ["pain", "fever", "infection", "injury"],
            "medium": ["discomfort", "concern", "check", "consultation"],
            "low": ["routine", "preventive", "general", "wellness"]
        }
    
    def _create_graph(self) -> StateGraph:
        """Create the LangGraph workflow"""
        workflow = StateGraph(AgentState)
        
        # Add nodes
        workflow.add_node("analyze_query", self.analyze_query)
        workflow.add_node("determine_urgency", self.determine_urgency)
        workflow.add_node("extract_specialization", self.extract_specialization)
        workflow.add_node("generate_recommendation", self.generate_recommendation)
        
        # Add edges
        workflow.set_entry_point("analyze_query")
        workflow.add_edge("analyze_query", "determine_urgency")
        workflow.add_edge("determine_urgency", "extract_specialization")
        workflow.add_edge("extract_specialization", "generate_recommendation")
        workflow.add_edge("generate_recommendation", END)
        
        return workflow.compile()
    
    def analyze_query(self, state: AgentState) -> AgentState:
        """Analyze the user's medical query"""
        query = state["user_query"].lower()
        
        # Extract potential symptoms
        symptoms = []
        symptom_keywords = [
            "pain", "ache", "fever", "cough", "headache", "nausea", 
            "dizziness", "fatigue", "rash", "swelling", "bleeding"
        ]
        
        for keyword in symptom_keywords:
            if keyword in query:
                symptoms.append(keyword)
        
        state["extracted_symptoms"] = symptoms
        return state
    
    def determine_urgency(self, state: AgentState) -> AgentState:
        """Determine the urgency level of the medical query"""
        query = state["user_query"].lower()
        urgency = "low"  # default
        
        for level, keywords in self.urgency_keywords.items():
            for keyword in keywords:
                if keyword in query:
                    urgency = level
                    break
            if urgency == "emergency":
                break
        
        state["urgency_level"] = urgency
        return state
    
    def extract_specialization(self, state: AgentState) -> AgentState:
        """Extract the most relevant medical specialization"""
        query = state["user_query"].lower()
        specialization = "general"  # default
        
        for keyword, spec in self.specialization_map.items():
            if keyword in query:
                specialization = spec
                break
        
        state["extracted_specialization"] = specialization
        return state
    
    def generate_recommendation(self, state: AgentState) -> AgentState:
        """Generate AI recommendation based on analysis"""
        urgency = state["urgency_level"]
        specialization = state["extracted_specialization"]
        symptoms = state["extracted_symptoms"]
        doctor_count = len(state.get("doctor_results", []))
        
        # Generate contextual recommendation
        if urgency == "emergency":
            recommendation = "⚠️ URGENT: Based on your symptoms, please seek immediate medical attention. Call emergency services (911) or go to the nearest emergency room."
        elif urgency == "high":
            recommendation = f"🚨 Your symptoms suggest you should see a doctor soon. I found {doctor_count} medical professionals nearby."
        else:
            recommendation = f"I found {doctor_count} medical professionals in your area."
        
        if specialization != "general":
            recommendation += f" Based on your query, I recommend consulting a {specialization} specialist."
        
        if symptoms:
            recommendation += f" Your mentioned symptoms: {', '.join(symptoms)}."
        
        # Add location-based advice
        if doctor_count > 0:
            recommendation += " The results are sorted by distance from your location."
        else:
            recommendation += " You may want to expand your search radius or consider telehealth options."
        
        state["ai_recommendation"] = recommendation
        return state
    
    def process_query(self, user_query: str, user_location: Dict[str, float], 
                     search_radius: float = 10.0, doctor_results: List[Dict] = None) -> Dict[str, Any]:
        """Process a medical query and return recommendations"""
        
        initial_state = AgentState(
            user_query=user_query,
            user_location=user_location,
            search_radius=search_radius,
            extracted_symptoms=[],
            extracted_specialization="",
            urgency_level="",
            doctor_results=doctor_results or [],
            ai_recommendation="",
            conversation_history=[]
        )
        
        # Run the graph
        result = self.graph.invoke(initial_state)
        
        return {
            "urgency_level": result["urgency_level"],
            "specialization": result["extracted_specialization"],
            "symptoms": result["extracted_symptoms"],
            "recommendation": result["ai_recommendation"],
            "should_call_emergency": result["urgency_level"] == "emergency"
        }

# Utility functions for the agent
def format_doctor_recommendation(doctors: List[Dict], ai_analysis: Dict) -> str:
    """Format doctor recommendations with AI insights"""
    if not doctors:
        return ai_analysis["recommendation"]
    
    recommendation = ai_analysis["recommendation"] + "\n\n"
    
    if ai_analysis["urgency_level"] == "emergency":
        recommendation += "🚨 EMERGENCY: Please call 911 immediately!\n\n"
    
    recommendation += "📍 Recommended doctors near you:\n"
    
    for i, doctor in enumerate(doctors[:3], 1):  # Show top 3
        recommendation += f"{i}. **{doctor['name']}** - {doctor['specialization']}\n"
        recommendation += f"   📍 {doctor['distance_km']}km away at {doctor['hospital']}\n"
        if doctor.get('rating'):
            recommendation += f"   ⭐ Rating: {doctor['rating']}/5\n"
        recommendation += "\n"
    
    return recommendation
